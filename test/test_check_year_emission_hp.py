#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_check_year_emission_hp.py
@Author: <PERSON>
@Date  : 2025/7/24
@Desc  : 测试年度执行报告污染物排放量超标检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.check.check_year_emission_hp import run

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试年度执行报告污染物排放量超标检查功能 ===")
    
    try:
        # 测试参数
        city = '九龙坡区'
        years = [2023]  # 支持整数格式
        
        print(f"测试城市: {city}")
        print(f"测试年份: {years}")
        print()
        
        # 执行检查
        result = run(city, years)
        
        # 输出结果
        print("=== 检查结果 ===")
        print(f"城市: {result['city']}")
        print(f"检查年份: {result['check_years']}")
        print(f"总超标记录数: {result['total_exceeded_count']}")
        print(f"水污染物超标记录数: {result['water_exceeded_count']}")
        print(f"大气污染物超标记录数: {result['air_exceeded_count']}")
        print()
        
        # 显示超标详情（前3条）
        if result['all_exceeded_list']:
            print("=== 超标线索详情（前3条）===")
            for i, record in enumerate(result['all_exceeded_list'][:3], 1):
                print(f"{i}. 企业: {record['enterprise_name']}")
                print(f"   污染物: {record['pollutant_name']} ({record['pollutant_type']})")
                print(f"   实际排放量: {record['actual_emission']:.2f} {record['emission_unit']}")
                print(f"   环评控制指标: {record['control_limit']:.2f} {record['emission_unit']}")
                print(f"   超标量: {record['exceed_amount']:.2f} {record['emission_unit']}")
                print(f"   超标比例: {record['exceed_ratio']:.1f}%")
                print(f"   报告年份: {record['report_year']}")
                print()
        else:
            print("未发现超标情况")
        
        print("测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_data_extraction():
    """测试数据提取功能"""
    print("=== 测试数据提取功能 ===")
    
    try:
        from lib.check.check_year_emission_hp import extract_execution_report_data, extract_environmental_assessment_data
        
        city = '九龙坡区'
        years = [2023]  # 支持整数格式
        
        # 测试执行报告数据提取
        print("测试执行报告数据提取...")
        water_exec, air_exec, name_id_map = extract_execution_report_data(city, years)
        print(f"水污染物执行报告数据: {len(water_exec)} 条")
        print(f"大气污染物执行报告数据: {len(air_exec)} 条")
        print(f"企业名称ID映射: {len(name_id_map)} 个")
        print()
        
        # 测试环评数据提取
        print("测试环评数据提取...")
        water_hp, air_hp, enterprise_map = extract_environmental_assessment_data(city)
        print(f"水污染物环评数据: {len(water_hp)} 条")
        print(f"大气污染物环评数据: {len(air_hp)} 条")
        print(f"企业关联映射: {len(enterprise_map)} 条")
        print()
        
        print("数据提取测试完成！")
        return True
        
    except Exception as e:
        print(f"数据提取测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试年度执行报告污染物排放量超标检查功能...")
    print()
    
    # 测试数据提取
    success1 = test_data_extraction()
    print()
    
    # 测试基本功能
    success2 = test_basic_functionality()
    
    if success1 and success2:
        print("\n所有测试通过！")
    else:
        print("\n部分测试失败，请检查错误信息。")
