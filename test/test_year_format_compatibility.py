#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_year_format_compatibility.py
@Author: <PERSON>
@Date  : 2025/7/24
@Desc  : 测试年份参数格式兼容性（整数和字符串）
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_year_format_conversion():
    """测试年份格式转换逻辑"""
    print("=== 测试年份格式转换逻辑 ===")
    
    # 测试不同格式的年份参数
    test_cases = [
        [2023, 2024],           # 整数列表
        ['2023', '2024'],       # 字符串列表
        [2023, '2024'],         # 混合格式
        ['2023'],               # 单个字符串
        [2023],                 # 单个整数
    ]
    
    for i, years in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {years} (类型: {[type(y).__name__ for y in years]})")
        
        # 模拟核心函数中的年份处理逻辑
        years_str = ', '.join([f"'{str(year)}年'" for year in years])
        print(f"  转换结果: {years_str}")
        
        # 验证转换是否正确
        expected_format = all('年' in f"'{str(year)}年'" for year in years)
        print(f"  格式正确: {expected_format}")
        print()
    
    print("年份格式转换测试完成！")

def test_api_parameter_conversion():
    """测试API参数转换逻辑"""
    print("=== 测试API参数转换逻辑 ===")
    
    # 模拟API接收到的整数年份参数
    api_years = [2021, 2022, 2023]
    print(f"API接收参数: {api_years} (类型: {type(api_years[0]).__name__})")
    
    # 模拟web_server.py中的转换逻辑
    years_str = [str(year) for year in api_years]
    print(f"转换为字符串: {years_str} (类型: {type(years_str[0]).__name__})")
    
    # 模拟核心函数中的处理
    db_format = ', '.join([f"'{str(year)}年'" for year in years_str])
    print(f"数据库查询格式: {db_format}")
    
    print("API参数转换测试完成！")

def test_database_query_format():
    """测试数据库查询格式"""
    print("=== 测试数据库查询格式 ===")
    
    # 模拟不同输入格式
    test_inputs = [
        [2023],
        ['2023'],
        [2023, 2024],
        ['2023', '2024']
    ]
    
    for years in test_inputs:
        print(f"输入: {years}")
        
        # 生成SQL查询中的年份部分
        years_str = ', '.join([f"'{str(year)}年'" for year in years])
        sql_fragment = f"report_time IN ({years_str})"
        print(f"SQL片段: {sql_fragment}")
        print()
    
    print("数据库查询格式测试完成！")

def main():
    """主测试函数"""
    print("开始测试年份参数格式兼容性...")
    print()
    
    test_year_format_conversion()
    print()
    
    test_api_parameter_conversion()
    print()
    
    test_database_query_format()
    
    print("✅ 所有年份格式兼容性测试通过！")

if __name__ == '__main__':
    main()
