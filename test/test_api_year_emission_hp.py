#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_api_year_emission_hp.py
@Author: <PERSON>
@Date  : 2025/7/24
@Desc  : 测试年度执行报告污染物排放量超标检查API接口
"""

import requests
import json
import time

# API配置
BASE_URL = "http://localhost:8088"
HEADERS = {
    "Content-Type": "application/json",
    "X-token": "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
}

def test_year_emission_vs_hp_api():
    """测试年度执行报告污染物排放量超标检查API"""
    print("=== 测试年度执行报告污染物排放量超标检查API ===")
    
    url = f"{BASE_URL}/executive-report/eia/year"
    
    # 测试数据
    test_data = {
        "city": "九龙坡区",
        "years": [2023]
    }
    
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print()
    
    try:
        # 发送POST请求
        response = requests.post(url, json=test_data, headers=HEADERS, timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功！")
            print()
            
            # 解析响应数据
            if result.get('status') == 'OK':
                data = result.get('data', {})
                print("=== 检查结果摘要 ===")
                print(f"城市: {data.get('city', 'N/A')}")
                print(f"检查年份: {data.get('check_years', [])}")
                print(f"总超标记录数: {data.get('total_exceeded_count', 0)}")
                print(f"水污染物超标记录数: {data.get('water_exceeded_count', 0)}")
                print(f"大气污染物超标记录数: {data.get('air_exceeded_count', 0)}")
                print()
                
                # 显示超标详情（前3条）
                all_exceeded = data.get('all_exceeded_list', [])
                if all_exceeded:
                    print("=== 超标线索详情（前3条）===")
                    for i, record in enumerate(all_exceeded[:3], 1):
                        print(f"{i}. 企业: {record.get('enterprise_name', 'N/A')}")
                        print(f"   污染物: {record.get('pollutant_name', 'N/A')} ({record.get('pollutant_type', 'N/A')})")
                        print(f"   实际排放量: {record.get('actual_emission', 0):.2f} {record.get('emission_unit', 't/a')}")
                        print(f"   环评控制指标: {record.get('control_limit', 0):.2f} {record.get('emission_unit', 't/a')}")
                        print(f"   超标量: {record.get('exceed_amount', 0):.2f} {record.get('emission_unit', 't/a')}")
                        print(f"   超标比例: {record.get('exceed_ratio', 0):.1f}%")
                        print(f"   报告年份: {record.get('report_year', 'N/A')}")
                        print()
                else:
                    print("未发现超标情况")
                
                return True
            else:
                print(f"API返回错误: {result}")
                return False
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False

def test_liveness():
    """测试服务器是否运行"""
    print("=== 测试服务器连通性 ===")
    
    url = f"{BASE_URL}/liveness"
    
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print("服务器运行正常")
            return True
        else:
            print(f"服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"无法连接到服务器: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试年度执行报告污染物排放量超标检查API...")
    print()
    
    # 测试服务器连通性
    if not test_liveness():
        print("服务器连通性测试失败，请启动服务器后重试")
        return
    
    print()
    
    # 测试主要API
    success = test_year_emission_vs_hp_api()
    
    print()
    if success:
        print("✅ API测试通过！")
    else:
        print("❌ API测试失败！")

if __name__ == '__main__':
    main()
