# -*- coding: utf-8 -*-
"""
@File  : check_hospital_bed.py.py
@Author: <PERSON>
@Date  : 2025/3/10 14:13
@Desc  : 
"""
import os
import pandas as pd

from lib import proj_dir, logger
from lib.check.base import timing_decorator, get_db_cursor


def get_data(city):
    licence = pd.read_csv(os.path.join(proj_dir, 'licence', city, '病床数.csv'))
    actual = pd.read_excel(os.path.join(proj_dir, 'file', '医院机构信息.xlsx'))
    return licence, actual


def check_hospital_bed(licence, actual):
    licence.rename(columns={'病床数': '许可证床位数'}, inplace=True)

    actual = actual[['机构名称', '统一社会信用代码', '设置单位', '床位数']]
    actual = actual[actual['床位数'].notnull()].copy()
    actual['机构名称'] = actual['机构名称'].str.replace(r'\（.*?\）', '', regex=True)
    actual.rename(columns={'床位数': '卫健委床位数'}, inplace=True)

    # 匹配顺序：统一社会信用代码 > 机构名称 > 设置单位
    ## 统一社会信用代码
    compare = []
    merge = pd.merge(licence, actual, how='inner', on='统一社会信用代码')
    if not merge.empty:
        compare.append(merge)
    actual = actual[~actual['统一社会信用代码'].isin(merge['统一社会信用代码'])].copy()
    licence = licence[~licence['统一社会信用代码'].isin(merge['统一社会信用代码'])]
    actual.drop(['统一社会信用代码'], axis=1, inplace=True)

    ## 机构名称
    merge = pd.merge(licence, actual, how='inner', left_on='单位名称', right_on='机构名称')
    if not merge.empty:
        compare.append(merge)
    actual = actual[~actual['机构名称'].isin(merge['机构名称'])]
    licence = licence[~licence['单位名称'].isin(merge['机构名称'])]

    ## 设置单位
    merge = pd.merge(licence, actual, how='inner', left_on='单位名称', right_on='设置单位')
    if not merge.empty:
        compare.append(merge)

    if not compare:
        return []
    compare = pd.concat(compare)

    exception = compare[compare['许可证床位数']!=compare['卫健委床位数']].copy()
    for col in ['许可证床位数', '卫健委床位数']:
        exception[col] = exception[col].astype(int)
    exception = exception.to_dict(orient='records')
    return exception


def run(city):
    licence, actual = get_data(city)
    exception = check_hospital_bed(licence, actual)
    return exception


@timing_decorator
def get_online_data(city):
    logger.info('拉取医院病床位数据...')
    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    statistics_table = get_table_name("cu_hospital_permit_bed_statistics", city)
    hospital_table = get_table_name("cu_basic_information_hospital", city)

    sql = f'''
        select
            w.f_enterprise_id, m.*
        from
            {statistics_table} m
            left join {hospital_table} w on m.hospital_name=w.cu_enterprise_name
        where
            permit_bed_num is not NULL and
            hospital_health_bed_num is not NULL and
            f_enterprise_id is not NULL and
            w.region_code in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    '''

    bed_data = db.query_sql(sql)
    logger.info('拉取医院病床位数据完成')
    return bed_data


def check_hospital_online(city):
    bed_data = get_online_data(city)
    data = bed_data[bed_data['permit_bed_num']!=bed_data['hospital_health_bed_num']]
    data = data[[
        'f_enterprise_id', 'uscc', 'permit_unit_name', 'permit_bed_num', 'hospital_name', 'hospital_set_unit',
        'hospital_health_bed_num'
    ]]
    data[['permit_bed_num', 'hospital_health_bed_num']] = data[['permit_bed_num', 'hospital_health_bed_num']].apply(lambda x: x.astype(int))
    data = data.replace({float('nan'): None}).to_dict('records')
    return data


if __name__ == '__main__':
    # exception = run('九龙坡区')
    # for item in exception:
    #     print(item)
    res = check_hospital_online('九龙坡区')
    print(res)