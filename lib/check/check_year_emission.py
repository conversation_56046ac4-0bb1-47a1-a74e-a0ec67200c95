# -*- coding: utf-8 -*-
"""
@File  : check_year_emission.py
@Author: <PERSON>
@Date  : 2025/3/5 17:44
@Desc  : 执行报告登记的污染物年排放量超过排污许可证年排放量许可限值
"""
import json
import os
import pandas as pd
import numpy as np
from collections import defaultdict
from thefuzz import process

from lib import ori_database_conf, ALL_ENTS, proj_dir
from mod import DB


def extract_data_from_db(city, years):
    """ 提取执行报告污染物年排放量 """
    host = ori_database_conf['host']
    port = ori_database_conf['port']
    dbname = ori_database_conf['database']
    user = ori_database_conf['user']
    password = ori_database_conf['password']
    db = DB(host, port, dbname, user, password)

    sql = """
        select f_enterprise_id, f_enterprise_name, f_report_time, f_report_content
        from t_pw_enterprise_zx_report t 
        where t.f_enterprise_name in ({ents}) and f_report_type = '年报'
    """
    ents = ', '.join([f"'{ent}'" for ent in ALL_ENTS[city]])
    data = db.query_sql(sql.format(ents=ents))
    db.close()

    data_dct = defaultdict(dict)
    data['f_report_time'] = data['f_report_time'].str.slice(0,4)
    data['f_report_time'] = data['f_report_time'].astype(int)
    data = data[data['f_report_time'].isin(years)]
    name_id_map = {}
    for (ent_id, ent_name), group in data.groupby(['f_enterprise_id', 'f_enterprise_name']):
        name_id_map[ent_name] = ent_id
        for _, row in group.iterrows():
            year = row['f_report_time']
            content = row['f_report_content']
            try:
                content = json.loads(content)
            except:
                pass
            content = content['data']
            try:
                if isinstance(content, str):
                    content = content.replace('\n', '').replace('\t', '')
                content = json.loads(content)
            except:
                pass

            if not content:
                continue
            try:
                content = content['emissionInfo']
            except:
                continue

            for emission, val in content.items():
                emssion_data = pd.DataFrame(val)
                if emssion_data.empty:
                    continue
                emssion_data = emssion_data[['portType', 'pollutantName', 'total']].copy()
                emssion_data['total'] = emssion_data['total'].apply(pd.to_numeric, errors='coerce')
                if year not in data_dct[ent_name]:
                    data_dct[ent_name][year] = {emission: emssion_data}
                else:
                    data_dct[ent_name][year][emission] = emssion_data

    return data_dct, name_id_map


def _fuzzy_match(name, dict_name, threshold=50):
    """模糊匹配企业名称"""

    match = process.extractOne(name, list(dict_name.keys()))
    if match[1] >= threshold:
        return dict_name[match[0]]
    else:
        print(f'模糊匹配企业名称失败: {name}')
        return ''


# license_number = pd.read_excel(f'{proj_dir}/licence/basic_information_enterprise_chongqing.xlsx',
#                                header=0, dtype={'pol_permit_number': str})
# license_number: dict = (license_number[['enterprise_name', 'pol_permit_number']]
#                         .set_index('enterprise_name').to_dict()['pol_permit_number'])
#
#
# def get_ents_licence_number(company_name: str) -> str:
#
#     if license_number.get(company_name, '') == '':
#         return _fuzzy_match(company_name, license_number)
#     else:
#         return license_number[company_name]


def extract_data_from_licence(city):
    """ 从排污许可证中提取数据 """
    prefix = '九龙坡区—' if city == '九龙坡区' else f'{city}_'

    rename = {
        '许可年排放量限值（t/a）-第一年': 'licence_val',
        '污染物名称': 'pollutantName',
        '排口类型': 'portType',
        '单位名称': 'ent_name'
    }
    
    dir_path = os.path.join(proj_dir, 'licence')
    org = pd.read_csv(os.path.join(dir_path, city, f'{prefix}有组织总排放.csv'))
    org.rename(columns=rename, inplace=True)
    org = org[list(rename.values())]
    # org['ent_name'] = org['ent_name'].apply(lambda name: _fuzzy_match(name, license_number))
    org['licence_val'] = org['licence_val'].apply(pd.to_numeric, errors='coerce')

    inorg = pd.read_csv(os.path.join(dir_path, city, f'{prefix}无组织总排放.csv'))
    inorg.rename(columns=rename, inplace=True)
    inorg = inorg[list(rename.values())]
    # inorg['ent_name'] = inorg['ent_name'].apply(lambda name: _fuzzy_match(name, license_number))
    inorg['licence_val'] = inorg['licence_val'].apply(pd.to_numeric, errors='coerce')

    total = pd.read_csv(os.path.join(dir_path, city, f'{prefix}总排放.csv'))
    total.rename(columns=rename, inplace=True)
    total = total[['ent_name', 'pollutantName', 'licence_val']]
    # total['ent_name'] = total['ent_name'].apply(lambda name: _fuzzy_match(name, license_number))
    total['licence_val'] = total['licence_val'].apply(pd.to_numeric, errors='coerce')

    return org, inorg, total


def check_year_emission(data_dct, name_id_map, org, inorg, total):

    def sum_with_nan(series):
        return np.nan if series.isnull().any() else series.sum()

    # 执行报告数据处理
    new_data_dct = {
        'airMainEmission': [],
        'airMinorEmission': [],
        'airTotalEmission': []
    }
    for ent_name, val1 in data_dct.items():
        for year, val2 in val1.items():
            for emission, data in val2.items():
                if not emission.startswith('air'):
                    continue
                data = data.groupby(['portType', 'pollutantName']).agg({'total': sum_with_nan}).reset_index()
                data['year'] = year
                data['ent_name'] = ent_name
                new_data_dct[emission].append(data)
    for emission, group in new_data_dct.items():
        if group:
            group = pd.concat(group)
            group.dropna(inplace=True, how='any')
            new_data_dct[emission] = group

    # 排污许可证数据处理
    total['portType'] = '全厂合计'
    licence_data = pd.concat([org, inorg, total])

    licence_airmain = licence_data[
        (licence_data['portType'] == '主要排放口合计') & (licence_data['licence_val'].notnull())
    ].copy()
    licence_airmain['portType'] = '主要排放口'

    licence_airminor = licence_data[licence_data['portType'].isin(['一般排放口合计', '全厂无组织排放总计'])]
    licence_airminor = (licence_airminor.groupby(['ent_name', 'portType', 'pollutantName']).
                        agg({'licence_val': sum_with_nan}).reset_index())
    licence_airminor = licence_airminor[licence_airminor['licence_val'].notnull()].copy()
    licence_airminor['portType'] = '一般排放口（合计）'

    licence_airtotal = licence_data[(licence_data['portType']=='全厂合计') & (licence_data['licence_val'].notnull())]

    # 对比
    if all(isinstance(item, list) for item in new_data_dct.values()):
        return []
    new_exe_data = pd.concat([item for item in new_data_dct.values() if not isinstance(item, list)])
    new_licence_data = pd.concat([licence_airmain, licence_airminor, licence_airtotal])
    join_cols = ['ent_name', 'portType', 'pollutantName']
    join_data = pd.merge(new_exe_data, new_licence_data, on=join_cols, how='inner')

    exception = join_data[join_data['total'] > join_data['licence_val']].copy()
    exception.drop_duplicates(inplace=True)
    exception.rename(columns={'total': 'exe_val'}, inplace=True)
    enterprise_id = exception['ent_name'].map(name_id_map)
    exception.insert(0, 'f_enterprise_id', enterprise_id)

    return exception.to_dict(orient='records')


def run(city, years):
    data_dct, name_id_map = extract_data_from_db(city, years)
    org, inorg, total = extract_data_from_licence(city)
    exception = check_year_emission(data_dct, name_id_map, org, inorg, total)
    return exception


if __name__ == '__main__':
    run()

