# -*- coding: utf-8 -*-
"""
@File  : base.py.py
@Author: <PERSON>
@Date  : 2025/1/7 14:39
@Desc  : 
"""
import os

import numpy as np
import pandas as pd
import json
from docx import Document
from lib import TABLE_RULES, TBL_NAME_MAP
from collections import defaultdict
import time
import threading
from tqdm import tqdm

from mod import DB
from lib import ori_database_conf, main_database_conf, ALL_ENTS, logger, Global, post, MyEncoder


__all__ = [
    '_m_base_cols',
    'm_airmain_cols',
    'm_airminor_cols',
    'm_airtotal_cols',
    'm_watermain_cols',
    'm_waterminor_cols',
    'm_watertotal_cols',

    '_q_base_cols',
    'q_airmain_cols',
    'q_airminor_cols',
    'q_airtotal_cols',
    'q_watermain_cols',
    'q_waterminor_cols',
    'q_watertotal_cols',

    '_y_base_cols',
    'y_airmain_cols',
    'y_airminor_cols',
    'y_airtotal_cols',
    'y_watermain_cols',
    'y_waterminor_cols',
    'y_watertotal_cols',

    'mq_idx2col',
    'my_idx2col',
    'qy_idx2col',

    'm_emission_map',
    'q_emission_map',
    'y_emission_map'
]

# 月报坐标字段
_m_base_cols = ['emitValue']
m_airmain_cols = ['portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode'] + _m_base_cols
m_airminor_cols = ['portType', 'pollutantName', 'pollutantCode'] + _m_base_cols
m_airtotal_cols = m_airminor_cols
m_watermain_cols = [
    'portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode',
] + _m_base_cols
m_waterminor_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode'] + _m_base_cols
m_watertotal_cols = m_waterminor_cols

total_cols= m_airmain_cols + m_watermain_cols + m_airminor_cols +m_waterminor_cols + m_watertotal_cols + m_airtotal_cols

mq_idx2col = {
    1: 'firstMonth',
    2: 'secondMonth',
    3: 'thirdMonth'
}

# 季报坐标字段
_q_base_cols = ['firstMonth', 'secondMonth', 'thirdMonth', 'total']
q_airmain_cols = [
    'portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode',
] + _q_base_cols
q_airminor_cols = ['portType', 'pollutantName', 'pollutantCode'] + _q_base_cols
q_airtotal_cols = q_airminor_cols
q_watermain_cols = [
    'portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode'
] + _q_base_cols
q_waterminor_cols = [
    'portType', 'emissionType', 'pollutantName', 'pollutantCode'
] + _q_base_cols
q_watertotal_cols = q_waterminor_cols

# 年报坐标字段
my_idx2col = {
    1: 'january',
    2: 'february',
    3: 'march',
    4: 'april',
    5: 'may',
    6: 'june',
    7: 'july',
    8: 'august',
    9: 'september',
    10: 'october',
    11: 'november',
    12: 'december'
}
qy_idx2col = {
    1: 'quarter1',
    2: 'quarter2',
    3: 'quarter3',
    4: 'quarter4'
}

_y_base_cols = [
    "january", "february", "march", "april", "may", "june", "july", "august", "september", "october",
    "november", "december", "quarter1", "quarter2", "quarter3", "quarter4"
]
y_airmain_cols = ['portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode'] + _y_base_cols
y_airminor_cols = ['portType', 'pollutantName', 'pollutantCode'] + _y_base_cols
y_airtotal_cols = y_airminor_cols
y_watermain_cols = [
                       'portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode'
                   ] + _y_base_cols
y_waterminor_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode'] + _y_base_cols
y_watertotal_cols = y_waterminor_cols

m_emission_map = {
    "airMainEmission": m_airmain_cols,
    "airMinorEmission": m_airminor_cols,
    "airTotalEmission": m_airtotal_cols,
    "waterMainEmission": m_watermain_cols,
    "waterMinorEmission": m_waterminor_cols,
    "waterTotalEmission": m_watertotal_cols
}

q_emission_map = {
    "airMainEmission": q_airmain_cols,
    "airMinorEmission": q_airminor_cols,
    "airTotalEmission": q_airtotal_cols,
    "waterMainEmission": q_watermain_cols,
    "waterMinorEmission": q_waterminor_cols,
    "waterTotalEmission": q_watertotal_cols
}

y_emission_map = {
    "airMainEmission": y_airmain_cols,
    "airMinorEmission": y_airminor_cols,
    "airTotalEmission": y_airtotal_cols,
    "waterMainEmission": y_watermain_cols,
    "waterMinorEmission": y_waterminor_cols,
    "waterTotalEmission": y_watertotal_cols
}


def listdir(path):
    return [item for item in os.listdir(path) if not item.startswith('~$')]


def to_multi_idx(data, row_num, col_num):
    """
    将给定的数据转换为具有多层索引的数据框。

    参数:
    data: pd.DataFrame, 输入的数据框。
    row_num: int, 用于定义行索引的列数。
    col_num: int, 用于定义列索引的行数。

    返回:
    pd.DataFrame, 具有多层索引的数据框。
    """
    # 提取并转置行索引数据
    row_idx = data.iloc[col_num:, :row_num].values.T
    # 提取列索引数据
    col_idx = data.iloc[:col_num, row_num:].values
    # 提取行索引的名称
    names = data.iloc[col_num - 1, :row_num].values

    # 如果行索引不为空，则创建多层索引；否则设置为None
    if row_idx.size != 0:
        row_idx = pd.MultiIndex.from_arrays(row_idx, names=names)
    else:
        row_idx = None

    # 如果列索引不为空，则创建多层索引；否则设置为None
    if col_idx.size != 0:
        col_idx = pd.MultiIndex.from_arrays(col_idx)
    else:
        col_idx = None

    # 使用提取的索引和原始数据创建新的DataFrame
    data = pd.DataFrame(data.iloc[col_num:, row_num:].values, columns=col_idx, index=row_idx)

    # 返回重新索引后的数据
    return data


def match_table_names(tables, _type):
    """
    将doc中所有表格与表名对应起来

    根据不同的时间类型(_type)，选择相应的表名映射规则和表格处理规则，
    然后根据映射规则更新表名，确保所有表都有一个标准的名称

    参数:
    tables: dict, 原始的表格名称映射
    _type: str, 时间类型，决定使用哪一套表名映射规则和表格处理规则

    返回:
    tables: dict, 更新后的表格名称映射
    rules: dict, 对应时间类型的表格处理规则
    """
    # 根据时间类型选择相应的表名映射规则和表格处理规则
    match _type:
        case 'year':
            tbl_name_map = TBL_NAME_MAP['year_table_name_map']
            rules = TABLE_RULES['year']
        case 'quarter':
            tbl_name_map = TBL_NAME_MAP['quarter_table_name_map']
            rules = TABLE_RULES['quarter']
        case 'month':
            tbl_name_map = TBL_NAME_MAP['month_table_name_map']
            rules = TABLE_RULES['month']
        case _:
            raise ValueError(_type)

    # 根据选定的表名映射规则更新表名
    for old, new in tbl_name_map.items():
        if old in tables:
            tables[new] = tables.pop(old)
    return tables, rules


def process_doc_all_tbls(tables, _type):
    """
    将docx中所有表转为多层索引

    此函数接收一个包含表格的字典和一个类型标识符，根据类型标识符匹配表格名称，
    并将匹配到的表格转换为多层索引形式。如果表格名称不在规则范围内，则抛出运行时错误。

    参数:
    tables (dict): 包含表格的字典，其中键为表格名称，值为表格数据
    _type (str): 类型标识符，用于匹配表格名称

    返回:
    dict: 转换为多层索引后的表格字典
    """
    # 匹配表格名称和类型，获取更新后的表格和规则
    tables, rules = match_table_names(tables, _type)

    # 遍历所有表格
    for name, table in tables.items():
        # 如果当前表格名称在规则中
        if name in rules:
            # 获取当前表格对应的规则
            _rules = rules[name]
            # 根据规则将当前表格转换为多层索引，并更新字典中的表格
            tables[name] = to_multi_idx(table, _rules['row_num'], _rules['col_num'])
        else:
            # 如果当前表格名称不在规则中，抛出运行时错误
            raise RuntimeError(name)

    # 返回转换后的表格字典
    return tables


def print_format(data):
    print(json.dumps(data, indent=4, ensure_ascii=False))


def extract_tbl_from_tbl(file_pth):
    """
    提取环评报告表格中嵌套的表格信息

    该函数读取一个Word文档文件，提取其中的表格信息，特别是嵌套在单元格中的子表格信息
    它将表格转换为pandas DataFrame对象，并以表格标题作为键存储在字典中

    参数:
    file_pth (str): Word文档文件的路径

    返回:
    tbls (dict): 包含所有提取的表格（包括嵌套表格）的字典，键为表格标题，值为pandas DataFrame对象
    """
    # 加载Word文档
    doc = Document(file_pth)

    # 初始化一个字典来存储表格
    tbls = {}

    # 遍历文档中的所有表格
    for tbl1 in doc.tables:
        # 初始化一个标志来判断表格中是否包含嵌套表格
        tiled = True

        # 初始化一个列表来存储表格行的数据
        df = []

        # 遍历表格中的每一行
        for row in tbl1.rows:
            # 初始化一个列表来存储当前行的数据
            df_row = []

            # 遍历当前行中的每一个单元格
            for cell in row.cells:
                # 如果单元格中包含子表格
                if cell.tables:
                    # 则将标志设置为False
                    tiled = False

                    # 遍历单元格中的所有子表格
                    for tbl2 in cell.tables:
                        # 初始化一个列表来存储子表格的数据
                        df2 = []

                        # 遍历子表格的每一行
                        for row2 in tbl2.rows:
                            # 提取子表格当前行的数据并存储到列表中
                            df2_row = [cell.text for cell in row2.cells]
                            df2.append(df2_row)

                        # 提取子表格的标题
                        title = tbl2._element.getprevious().text

                        # 将子表格数据存储到字典中
                        tbls[title] = pd.DataFrame(df2)

                # 如果当前表格不包含嵌套表格
                elif tiled:
                    # 将单元格文本添加到当前行的数据列表中
                    df_row.append(cell.text)

            # 如果当前表格不包含嵌套表格
            if tiled:
                # 将当前行的数据添加到表格数据列表中
                df.append(df_row)

        # 如果当前表格不包含嵌套表格
        if tiled:
            # 提取当前表格的标题
            title = tbl1._element.getprevious().text

            # 将当前表格数据存储到字典中
            tbls[title] = pd.DataFrame(df)

    # 返回包含所有表格信息的字典
    return tbls


def clean_null(data):
    """ 删除值为空的项 """
    if not isinstance(data, dict):
        return data
    delete = []
    for k, v in data.items():
        if not clean_null(v):
            delete.append(k)
    for k in delete:
        del data[k]
    return data


def timing_decorator(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()  # 获取开始时间
        result = func(*args, **kwargs)  # 执行被装饰的函数
        end_time = time.time()  # 获取结束时间
        execution_time = end_time - start_time  # 计算执行时间
        logger.info(f"Function '{func.__name__}' executed in {execution_time:.4f} seconds.")
        return result  # 返回原函数的结果
    return wrapper


def save_data_to_pickle(data, file_path):
    data.to_pickle(file_path)
    logger.info(f'db data saved to {file_path}')


def get_db_cursor(name='main'):
    if name == 'ori':
        host = ori_database_conf['host']
        port = ori_database_conf['port']
        dbname = ori_database_conf['database']
        user = ori_database_conf['user']
        password = ori_database_conf['password']
    elif name == 'main':
        host = main_database_conf['host']
        port = main_database_conf['port']
        dbname = main_database_conf['database']
        user = main_database_conf['user']
        password = main_database_conf['password']
    db = DB(host, port, dbname, user, password)
    return db


def concat_clean_data(city):
    from lib.check.table_name_generator import get_table_name

    db = get_db_cursor('main')
    ents = ', '.join([f"'{ent}'" for ent in ALL_ENTS[city]])

    # 动态生成表名
    table_name = get_table_name("zxgk_basic_information_enterprise_report", city)
    sql = f"""
        select {{cols}}
        from {table_name}
        where f_enterprise_name in ({{ents}})
    """
    main_cols = ['f_enterprise_id', 'f_enterprise_name', 'f_report_type', 'f_report_time']
    companyInfo_dct = {
        'report_time': 'reportType',
        'permit_code': 'permitCode',
        'enterprise_name_js': 'enterName',
        'report_submit_time': 'reportSubmitTime',
        'issue_authority': 'issueAuthority',
        'report_time': 'reportTime'
    }
    cols = ', '.join(main_cols + list(companyInfo_dct.keys()) + ['report_id'])
    base = db.query_sql(sql.format(ents=ents, cols=cols))

    report_ids = base['report_id'].unique().tolist()
    report_ids_str = '(' + ','.join(["\'"+str(rid)+"\'" for rid in report_ids]) + ')'

    sql_template = """ select * from {tbl} where report_id in """ + report_ids_str

    # 获取月报、季报、年报 废水、废气数据 - 使用动态表名
    water_month_table = get_table_name("zxgk_basic_data_water_month_report", city)
    water_quarter_table = get_table_name("zxgk_basic_data_water_quart_report", city)
    water_year_table = get_table_name("zxgk_basic_data_water_year_report", city)
    gas_month_table = get_table_name("zxgk_basic_data_gas_month_report", city)
    gas_quarter_table = get_table_name("zxgk_basic_data_gas_quart_report", city)
    gas_year_table = get_table_name("zxgk_basic_data_gas_year_report", city)

    water_month = sql_template.format(tbl=water_month_table)
    water_quarter = sql_template.format(tbl=water_quarter_table)
    water_year = sql_template.format(tbl=water_year_table)
    gas_month = sql_template.format(tbl=gas_month_table)
    gas_quarter = sql_template.format(tbl=gas_quarter_table)
    gas_year = sql_template.format(tbl=gas_year_table)
    logger.info(f'开始拉取表`{water_month_table}`数据...')
    water_month = db.query_sql(water_month).fillna(np.nan)
    logger.info(f'开始拉取表`{water_quarter_table}`数据...')
    water_quarter = db.query_sql(water_quarter).fillna(np.nan)
    logger.info(f'开始拉取表`{water_year_table}`数据...')
    water_year = db.query_sql(water_year).fillna(np.nan)
    logger.info(f'开始拉取表`{gas_month_table}`数据...')
    gas_month = db.query_sql(gas_month).fillna(np.nan)
    logger.info(f'开始拉取表`{gas_quarter_table}`数据...')
    gas_quarter = db.query_sql(gas_quarter).fillna(np.nan)
    logger.info(f'开始拉取表`{gas_year_table}`数据...')
    gas_year = db.query_sql(gas_year).fillna(np.nan)
    water_month_dct = {report_id:group for report_id, group in water_month.groupby('report_id')}
    water_quarter_dct = {report_id:group for report_id, group in water_quarter.groupby('report_id')}
    water_year_dct = {report_id:group for report_id, group in water_year.groupby('report_id')}
    gas_month_dct = {report_id:group for report_id, group in gas_month.groupby('report_id')}
    gas_quarter_dct = {report_id:group for report_id, group in gas_quarter.groupby('report_id')}
    gas_year_dct = {report_id:group for report_id, group in gas_year.groupby('report_id')}

    import pickle

    # def save_data_to_pickle(data, file_path):
    #     pickle.dump(data, open(file_path, 'wb'))
    #
    # # # 将前面6个dct保存成pickle本地对象，从本地加载
    # save_data_to_pickle(water_month_dct, 'water_month_dct.pkl')
    # save_data_to_pickle(water_quarter_dct, 'water_quarter_dct.pkl')
    # save_data_to_pickle(water_year_dct, 'water_year_dct.pkl')
    # save_data_to_pickle(gas_month_dct, 'gas_month_dct.pkl')
    # save_data_to_pickle(gas_quarter_dct, 'gas_quarter_dct.pkl')
    # save_data_to_pickle(gas_year_dct, 'gas_year_dct.pkl')

    # # 从本地加载
    # prefix = ''   # '' ../../bin/
    # water_month_dct = pickle.load(open(prefix+'water_month_dct.pkl', 'rb'))
    # water_quarter_dct = pickle.load(open(prefix+'water_quarter_dct.pkl', 'rb'))
    # water_year_dct = pickle.load(open(prefix+'water_year_dct.pkl', 'rb'))
    # gas_month_dct = pickle.load(open(prefix+'gas_month_dct.pkl', 'rb'))
    # gas_quarter_dct = pickle.load(open(prefix+'gas_quarter_dct.pkl', 'rb'))
    # gas_year_dct = pickle.load(open(prefix+'gas_year_dct.pkl', 'rb'))

    new_records = []
    for _, row in tqdm(base.iterrows(), total=len(base), desc='执行报告数据处理'):
        report_id = row['report_id']
        new_record = {
            col: row[col] for col in main_cols
        }

        companyInfo = {
            v: row[k]
            for k,v in companyInfo_dct.items()
        }

        emissionInfo = {}
        month_map = {
            'pollutantName': 'pollutant_name',
            'outletName': 'outlet_name',
            'portType': 'port_type_name',
            'pollutantCode': 'pollutant_code',
            'emitValue': 'emit_value',
            'emissionType': 'emission_type',
            'outletCode': 'outlet_code'
        }
        month_map_reverse = {v:k for k,v in month_map.items()}
        quarter_map = {
            'emissionType': 'emission_type',
            'firstMonth': 'first_month',
            'outletCode': 'outlet_code',
            'outletName': 'outlet_name',
            'pollutantCode': 'pollutant_code',
            'pollutantName': 'pollutant_name',
            'portType': 'port_type_name',
            'secondMonth': 'second_month',
            'thirdMonth': 'third_month',
            'total': 'total'
        }
        quarter_map_reverse = {v:k for k,v in quarter_map.items()}
        year_map = {
            'april': 'april',
            'august': 'august',
            'december': 'december',
            'emissionType': 'emission_type',
            'february': 'february',
            'january': 'january',
            'july': 'july',
            'june': 'june',
            'march': 'march',
            'may': 'may',
            'november': 'november',
            'october': 'october',
            'outletCode': 'outlet_code',
            'outletName': 'outlet_name',
            'pollutantCode': 'pollutant_code',
            'pollutantName': 'pollutant_name',
            'portType': 'port_type_name',
            'quarter1': 'quarter1',
            'quarter2': 'quarter2',
            'quarter3': 'quarter3',
            'quarter4': 'quarter4',
            'september': 'september'
        }
        year_map_reverse = {v:k for k,v in year_map.items()}

        # 检查
        for cols in [
            m_airmain_cols, m_airminor_cols, m_airtotal_cols,
            m_watermain_cols, m_waterminor_cols, m_watertotal_cols
        ]:
            assert set(cols).issubset(set(month_map.keys()))
        for col in [
            q_airmain_cols, q_airminor_cols, q_airtotal_cols,
            q_watermain_cols, q_waterminor_cols, q_watertotal_cols
        ]:
            assert set(col).issubset(set(quarter_map.keys()))
        for col in [
            y_airmain_cols, y_airminor_cols, y_airtotal_cols,
            y_watermain_cols, y_waterminor_cols, y_watertotal_cols
        ]:
            assert set(col).issubset(set(year_map.keys()))

        _type = row['f_report_type']
        if _type == '月报':
            # 月报
            m_air = gas_month_dct.get(report_id, [])
            if isinstance(m_air, pd.DataFrame):
                for m_cols, emission, _type in zip(
                    [m_airmain_cols, m_airminor_cols, m_airtotal_cols],
                    ['airMainEmission', 'airMinorEmission', 'airTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_m_air = m_air[m_air['port_type']==_type]
                    tmp_cols = [month_map[k] for k in m_cols]
                    m_air_emission = tmp_m_air[tmp_cols].copy().rename(columns=month_map_reverse)
                    json_str = m_air_emission.to_json(orient='records', force_ascii=False)
                    m_air_emission = json.loads(json_str)
                    emissionInfo[emission] = m_air_emission

            m_water = water_month_dct.get(report_id, [])
            if isinstance(m_water, pd.DataFrame):
                for m_cols, emission, _type in zip(
                    [m_watermain_cols, m_waterminor_cols, m_watertotal_cols],
                    ['waterMainEmission', 'waterMinorEmission', 'waterTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_m_water = m_water[m_water['port_type'] == _type]
                    tmp_cols = [month_map[k] for k in m_cols]
                    m_water_emission = tmp_m_water[tmp_cols].copy().rename(columns=month_map_reverse)
                    json_str = m_water_emission.to_json(orient='records', force_ascii=False)
                    m_water_emission = json.loads(json_str)
                    emissionInfo[emission] = m_water_emission
        elif _type == '季报':
            # 季报
            q_air = gas_quarter_dct.get(report_id, [])
            if isinstance(q_air, pd.DataFrame):
                for q_cols, emission, _type in zip(
                    [q_airmain_cols, q_airminor_cols, q_airtotal_cols],
                    ['airMainEmission', 'airMinorEmission', 'airTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_q_air = q_air[q_air['port_type'] == _type]
                    tmp_cols = [quarter_map[k] for k in q_cols]
                    q_air_emission = tmp_q_air[tmp_cols].copy().rename(columns=quarter_map_reverse)
                    json_str = q_air_emission.to_json(orient='records', force_ascii=False)
                    q_air_emission = json.loads(json_str)
                    emissionInfo[emission] = q_air_emission
            q_water = water_quarter_dct.get(report_id, [])
            if isinstance(q_water, pd.DataFrame):
                for q_cols, emission, _type in zip(
                    [q_watermain_cols, q_waterminor_cols, q_watertotal_cols],
                    ['waterMainEmission', 'waterMinorEmission', 'waterTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_q_water = q_water[q_water['port_type'] == _type]
                    tmp_cols = [quarter_map[k] for k in q_cols]
                    q_water_emission = tmp_q_water[tmp_cols].copy().rename(columns=quarter_map_reverse)
                    json_str = q_water_emission.to_json(orient='records', force_ascii=False)
                    q_water_emission = json.loads(json_str)
                    emissionInfo[emission] = q_water_emission
        elif _type == '年报':
            # 年报
            y_air = gas_year_dct.get(report_id, [])
            if isinstance(y_air, pd.DataFrame):
                for y_cols, emission, _type in zip(
                    [y_airmain_cols, y_airminor_cols, y_airtotal_cols],
                    ['airMainEmission', 'airMinorEmission', 'airTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_y_air = y_air[y_air['port_type'] == _type]
                    tmp_cols = [year_map[k] for k in y_cols]
                    y_air_emission = tmp_y_air[tmp_cols].copy().rename(columns=year_map_reverse)
                    json_str = y_air_emission.to_json(orient='records', force_ascii=False)
                    y_air_emission = json.loads(json_str)
                    emissionInfo[emission] = y_air_emission
            y_water = water_year_dct.get(report_id, [])
            if isinstance(y_water, pd.DataFrame):
                for y_cols, emission, _type in zip(
                    [y_watermain_cols, y_waterminor_cols, y_watertotal_cols],
                    ['waterMainEmission', 'waterMinorEmission', 'waterTotalEmission'],
                    ['main', 'minor', 'total']
                ):
                    tmp_y_water = y_water[y_water['port_type'] == _type]
                    tmp_cols = [year_map[k] for k in y_cols]
                    y_water_emission = tmp_y_water[tmp_cols].copy().rename(columns=year_map_reverse)
                    json_str = y_water_emission.to_json(orient='records', force_ascii=False)
                    y_water_emission = json.loads(json_str)
                    emissionInfo[emission] = y_water_emission
        else:
            logger.error(f'未知的报告类型: {_type}')

        f_report_content = {
            'data': {
                'companyInfo': companyInfo,
                'emissionInfo': emissionInfo
            }
        }
        f_report_content = json.dumps(f_report_content, ensure_ascii=False, cls=MyEncoder)
        new_record['f_report_content'] = f_report_content
        new_records.append(new_record)

    data = pd.DataFrame(new_records)
    return data


@timing_decorator
def get_exe_data(city):
    """ 拉取全部企业执行报告database数据 """
    logger.info(f'拉取{city}全部企业执行报告database数据...')
    if city in Global.GET_EXE_DATA:
        return Global.GET_EXE_DATA[city]

    if not post:
        db = get_db_cursor(name='ori')

        sql = """
            select f_enterprise_id, f_enterprise_name, f_report_type, f_report_time, f_report_content
            from t_pw_enterprise_zx_report t
            where t.f_enterprise_name in ({ents})
        """
        ents = ', '.join([f"'{ent}'" for ent in ALL_ENTS[city]])
        data = db.query_sql(sql.format(ents=ents))

        # data = db.query_sql("select * from t_pw_enterprise_zx_report t where t.f_enterprise_name = '土默特左旗城投环保有限公司金山污水处理厂';")

        db.close()
    else:
        data = concat_clean_data(city)

    thread = threading.Thread(target=save_data_to_pickle, args=(data, 'df_cache.pkl'))
    thread.start()

    logger.info(f'拉取{city}全部企业执行报告database数据完成')
    Global.GET_EXE_DATA[city] = data
    return Global.GET_EXE_DATA[city]


def list_diff(a, b):
    b_count = defaultdict(int)
    for x in b:
        b_count[x] += 1
    result = []
    for x in a:
        if b_count[x] > 0:
            b_count[x] -= 1
        else:
            result.append(x)
    return result


if __name__ == '__main__':
    # base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年环评项目电子档案资料'
    # path = os.path.join(base_path, '渝（九）环准〔2022〕017号重庆诚创塑胶有限公司废旧塑料回收加工项目（陶家镇）/废旧塑料水管回收加工项目.docx')
    # res = extract_tbl_from_tbl(path)
    # for name, tbl in res.items():
    #     print(name)
    #     # print(tbl)

    concat_clean_data('九龙坡区')
