# -*- coding: utf-8 -*-
"""
@File  : check_year_emission_hp.py
@Author: <PERSON>
@Date  : 2025/7/22 15:41
@Desc  : ZXBG02-1-年度执行报告"实际排放情况及达标判定分析"中记载的污染物年排放量超过建设项目环境影响评价规定的总量控制指标
"""
import pandas as pd
import numpy as np
from collections import defaultdict

from lib import ALL_ENTS, logger
from lib.check.base import get_db_cursor, timing_decorator


def extract_execution_report_data(city, years):
    """
    提取执行报告污染物年排放量数据

    Args:
        city (str): 城市名称
        years (list): 年份列表（可以是字符串或整数）

    Returns:
        tuple: (水污染物数据, 大气污染物数据, 企业名称ID映射)
    """
    logger.info(f'开始提取{city}执行报告污染物年排放量数据...')

    db = get_db_cursor('main')

    # 获取企业列表
    ents = ', '.join([f"'{ent}'" for ent in ALL_ENTS[city]])
    # 确保年份格式正确，数据库中存储的是"2023年"格式
    years_str = ', '.join([f"'{str(year)}年'" for year in years])

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    water_table = get_table_name("zxgk_basic_data_water_year_report", city)

    # 提取水污染物年报数据 - 仅筛选port_type='total'的数据
    water_sql = f"""
        SELECT
            f_enterprise_id,
            f_enterprise_name,
            report_time,
            pollutant_code,
            pollutant_name,
            std_code,
            total,
            port_type,
            port_type_name
        FROM {water_table}
        WHERE f_enterprise_name IN ({ents})
            AND port_type = 'total'
            AND report_time IN ({years_str})
            AND total IS NOT NULL
            AND std_code IS NOT NULL
            AND region_code in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    """

    logger.info('提取年报水污染物年排放执行报告数据...')
    water_data = db.query_sql(water_sql)

    # 提取大气污染物年报数据 - 仅筛选port_type='total'的数据
    air_table = get_table_name("zxgk_basic_data_gas_year_report", city)
    air_sql = f"""
        SELECT
            f_enterprise_id,
            f_enterprise_name,
            report_time,
            pollutant_code,
            pollutant_name,
            std_code,
            total,
            port_type,
            port_type_name
        FROM {air_table}
        WHERE f_enterprise_name IN ({ents})
            AND port_type = 'total'
            AND report_time IN ({years_str})
            AND total IS NOT NULL
            AND std_code IS NOT NULL
            AND region_code in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    """

    logger.info('提取大气污染物执行报告年排放数据...')
    air_data = db.query_sql(air_sql)

    db.close()

    # 创建企业名称ID映射
    name_id_map = {}
    for df in [water_data, air_data]:
        if not df.empty:
            for _, row in df.iterrows():
                name_id_map[row['f_enterprise_name']] = row['f_enterprise_id']

    logger.info(f'执行报告年报数据提取完成 - 水污染物: {len(water_data)} 条, 大气污染物: {len(air_data)} 条')

    return water_data, air_data, name_id_map


def extract_environmental_assessment_data(city):
    """
    提取环评污染物年度总量控制指标数据

    Args:
        city (str): 城市名称

    Returns:
        tuple: (水污染物环评数据, 大气污染物环评数据)
    """
    logger.info(f'开始提取{city}环评污染物年度总量控制指标数据...')

    db = get_db_cursor('main')

    # 获取企业关联映射表
    from lib.check.table_name_generator import get_table_name
    map_table = get_table_name("basic_information_enterprise_map", city)
    map_sql = f"""
        SELECT
            f_enterprise_id,
            f_enterprise_name,
            hp_enterprise_id,
            hp_enterprise_name
        FROM {map_table}
        WHERE f_enterprise_id IS NOT NULL
            AND hp_enterprise_id IS NOT NULL
            AND region_code_city in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    """

    logger.info('提取企业关联映射数据...')
    enterprise_map = db.query_sql(map_sql)

    # 获取有映射关系的环评企业ID列表
    hp_enterprise_ids = enterprise_map['hp_enterprise_id'].dropna().unique().tolist()
    if not hp_enterprise_ids:
        logger.warning('未找到有效的企业关联映射数据')
        db.close()
        return pd.DataFrame(), pd.DataFrame(), enterprise_map

    hp_ents = ', '.join([f"'{ent_id}'" for ent_id in hp_enterprise_ids])

    # 提取水污染物环评数据
    water_hp_table = get_table_name("hp_basic_data_water_pollutant_annual_emission", city)
    water_hp_sql = f"""
        SELECT
            hp_enterprise_id,
            hp_enterprise_name,
            pollutant,
            annual_emission,
            std_code,
            emission_limit_unit
        FROM {water_hp_table}
        WHERE hp_enterprise_id IN ({hp_ents})
            AND annual_emission IS NOT NULL
            AND std_code IS NOT NULL
            AND region_code in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    """

    logger.info('提取水污染物总量控制环评数据...')
    water_hp_data = db.query_sql(water_hp_sql)

    # 提取大气污染物环评数据
    air_hp_table = get_table_name("hp_basic_data_air_pollutant_annual_emission", city)
    air_hp_sql = f"""
        SELECT
            hp_enterprise_id,
            hp_enterprise_name,
            pollutant,
            annual_emission,
            std_code,
            emission_limit_unit
        FROM {air_hp_table}
        WHERE hp_enterprise_id IN ({hp_ents})
            AND annual_emission IS NOT NULL
            AND std_code IS NOT NULL
            AND region_code in (
                select code from dim_ref_comm_country where value = '{city}'
            )
    """

    logger.info('提取大气污染物总量控制环评数据...')
    air_hp_data = db.query_sql(air_hp_sql)

    db.close()

    logger.info(f'环评总量控制数据提取完成 - 水污染物: {len(water_hp_data)} 条, 大气污染物: {len(air_hp_data)} 条')

    return water_hp_data, air_hp_data, enterprise_map


def compare_emission_data(exec_data, hp_data, enterprise_map, pollutant_type):
    """
    比较执行报告和环评数据，识别超标情况

    Args:
        exec_data (pd.DataFrame): 执行报告数据
        hp_data (pd.DataFrame): 环评数据
        enterprise_map (pd.DataFrame): 企业关联映射
        pollutant_type (str): 污染物类型 ('water' 或 'air')

    Returns:
        list: 超标线索清单
    """
    if exec_data.empty or hp_data.empty or enterprise_map.empty:
        logger.warning(f'执行报告-环评总量控制：{pollutant_type}污染物数据为空，跳过比较')
        return []

    logger.info(f'开始比较{pollutant_type}污染物执行报告与环评数据总量控制...')

    # 通过企业关联表建立映射关系
    exec_with_map = pd.merge(
        exec_data,
        enterprise_map[['f_enterprise_id', 'hp_enterprise_id']],
        on=['f_enterprise_id'],
        how='inner'
    )

    if exec_with_map.empty:
        logger.warning(f'{pollutant_type}污染物执行报告数据与企业映射表无匹配记录')
        return []

    # 与环评数据关联，通过hp_enterprise_id和std_code匹配
    joined_data = pd.merge(
        exec_with_map,
        hp_data,
        left_on=['hp_enterprise_id', 'std_code'],
        right_on=['hp_enterprise_id', 'std_code'],
        how='inner',
        suffixes=('_exec', '_hp')
    )

    if joined_data.empty:
        logger.warning(f'{pollutant_type}污染物执行报告与环评数据无匹配记录')
        return []

    # 数据类型转换
    joined_data['total'] = pd.to_numeric(joined_data['total'], errors='coerce')
    joined_data['annual_emission'] = pd.to_numeric(joined_data['annual_emission'], errors='coerce')

    # 过滤掉无效数据
    joined_data = joined_data.dropna(subset=['total', 'annual_emission'])

    # 识别超标情况：执行报告排放量 > 环评总量控制指标
    exceeded_data = joined_data[joined_data['total'] > joined_data['annual_emission']].copy()

    if exceeded_data.empty:
        logger.info(f'执行报告-环评总量控制指标{pollutant_type}污染物未发现超标情况')
        return []

    # 构建超标线索清单
    exceeded_list = []
    for _, row in exceeded_data.iterrows():
        exceeded_record = {
            'pollutant_type': pollutant_type,  # 污染物类型（water/air）
            'f_enterprise_id': row['f_enterprise_id'],  # 执行报告企业ID
            'f_enterprise_name': row['f_enterprise_name'],  # 执行报告企业名称
            'hp_enterprise_id': row['hp_enterprise_id'],  # 环评企业ID
            'hp_enterprise_name': row['hp_enterprise_name'],  # 环评企业名称
            'report_year': row['report_time'],  # 报告年份
            'pollutant_name': row['pollutant_name'],  # 污染物名称
            'pollutant_code': row['pollutant_code'],  # 污染物编码
            'std_code': row['std_code'],  # 标准编码
            'actual_emission': float(row['total']),  # 实际排放量
            'control_limit': float(row['annual_emission']),  # 环评控制指标
            'exceed_amount': round(float(row['total'] - row['annual_emission']), 5),  # 超标量
            'exceed_ratio': f"{(row['total'] - row['annual_emission']) / row['annual_emission'] * 100:.2f}%",  # 超标比例(%)
            'emission_unit': row.get('emission_limit_unit', 't/a')  # 排放量单位
        }
        exceeded_list.append(exceeded_record)

    logger.info(f'{pollutant_type}污染物发现超标记录: {len(exceeded_list)} 条')

    return exceeded_list


@timing_decorator
def check_year_emission_vs_hp(city, years):
    """
    检查年度执行报告污染物排放量是否超过环评总量控制指标

    Args:
        city (str): 城市名称
        years (list): 检查年份列表（可以是字符串或整数）

    Returns:
        dict: 检查结果，包含水污染物和大气污染物的超标线索
    """
    logger.info(f'开始检查{city} {years}年度执行报告污染物排放量超标情况...')

    try:
        # 提取执行报告数据
        water_exec, air_exec, name_id_map = extract_execution_report_data(city, years)

        # 提取环评数据
        water_hp, air_hp, enterprise_map = extract_environmental_assessment_data(city)

        # 比较水污染物数据
        water_exceeded = compare_emission_data(water_exec, water_hp, enterprise_map, 'water')

        # 比较大气污染物数据
        air_exceeded = compare_emission_data(air_exec, air_hp, enterprise_map, 'air')

        # 汇总结果
        total_exceeded = water_exceeded + air_exceeded

        logger.info(f'执行报告-环评总量控制指标检查完成 - 总超标记录: {len(total_exceeded)} 条 '
                    f'(水污染物: {len(water_exceeded)} 条, 大气污染物: {len(air_exceeded)} 条)')

        return total_exceeded

    except Exception as e:
        logger.error(f'执行报告-环评总量控制指标检查过程中发生错误: {str(e)}')
        raise


def run(city, years):
    """
    主函数：执行年度执行报告污染物排放量超标检查

    Args:
        city (str): 城市名称
        years (list): 检查年份列表（可以是字符串或整数）

    Returns:
        dict: 检查结果
    """
    return check_year_emission_vs_hp(city, years)


if __name__ == '__main__':
    # 示例调用
    city = '九龙坡区'
    years = [2023, 2024]

    result = run(city, years)

    print(result)
