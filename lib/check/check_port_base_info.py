# -*- coding: utf-8 -*-
"""
@File  : check_port_base_info.py
@Author: <PERSON>
@Date  : 2025/7/1 9:46
@Desc  : 
"""
import math
import pandas as pd

from lib import logger
from lib.check.base import timing_decorator, get_db_cursor


@timing_decorator
def get_data1(city):
    logger.info('拉取环评、许可证排口基本信息...')
    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    mapping_table = get_table_name("pw_basic_map_hp_map_pw_monitor_sit", city)

    sql = f'''
        select * from {mapping_table}
        where region_code in (
            select code from dim_ref_comm_country where value = '{city}'
        )
    '''
    hp_pw_basic_map = db.query_sql(sql)
    logger.info('拉取环评、许可证排口基本信息完成')
    return hp_pw_basic_map


@timing_decorator
def get_data2(city):
    logger.info('拉取环评、许可证排口经纬度信息...')
    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    mapping_table = get_table_name("pw_basic_map_hp_map_pw_monitor_sit", city)
    emission_outlet_table = get_table_name("pw_basic_information_air_emission_outlet", city)
    exhaust_pipe_table = get_table_name("hp_basic_information_exhaust_pipe_info", city)

    sql = f'''
    select *
    from (
            select *
            from {mapping_table}
            where region_code=(select code from dim_ref_comm_country where value = '{city}')
        ) mid
        left join (
            select
                f_enterprise_id,
                outlet_code as pw_outlet_code,
                permit_exhaust_lon,
                permit_exhaust_lat,
                permit_outlet_height as permit_exhaust_height,
                permit_exhaust_diameter
            from {emission_outlet_table}
            where region_code=(select code from dim_ref_comm_country where value = '{city}')
        ) pw
        using (f_enterprise_id, pw_outlet_code)
        -- on mid.f_enterprise_id = pw.f_enterprise_id and mid.pw_outlet_code = pw.pw_outlet_code
        left join (
            select
                hp_enterprise_id,
                outlet_code as hp_outlet_code,
                eia_exhaust_height,
                eia_exhaust_diameter,
                eia_exhaust_lon,
                eia_exhaust_lat
            from {exhaust_pipe_table}
            where region_code=(select code from dim_ref_comm_country where value = '{city}')
        ) hp
        using (hp_enterprise_id, hp_outlet_code)
        --on mid.hp_enterprise_id = hp.hp_enterprise_id and mid.hp_outlet_code = hp.hp_outlet_code
    '''
    data = db.query_sql(sql.format(city=city))
    logger.info('拉取环评、许可证排口经纬度信息完成')
    return data


def haversine_distance(lat1, lon1, lat2, lon2):
    # 将经纬度从度数转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)

    # 经纬度的差值
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad

    # Haversine公式
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    # 地球半径（单位：米）
    R = 6371000  # 平均半径约为6371公里
    distance = R * c

    return distance


def check_gas_port(city, threshold=100):
    ''' XKZ-04-2-许可证记载的污染物排气筒高度/排气筒内径/经纬度与环评不匹配 '''
    data = get_data2(city)
    data.drop(['id'], axis=1, inplace=True)
    res = []
    for _, row in data.iterrows():
        mismatch = []
        # 高度校验
        if row['permit_exhaust_height'] and row['eia_exhaust_height']:
            try:
                row['permit_exhaust_height'] = float(row['permit_exhaust_height'])
                row['eia_exhaust_height'] = float(row['eia_exhaust_height'])
            except:
                logger.error(f'uscc: {row["uscc"]} permit_exhaust_height: {row["permit_exhaust_height"]} '
                             f'eia_exhaust_height: {row["eia_exhaust_height"]} 高度数据转换失败')
            else:
                if abs(row['permit_exhaust_height'] - row['eia_exhaust_height']) > 0.001:
                    mismatch.append('高度')

        # 内径校验
        if row['permit_exhaust_diameter'] and row['eia_exhaust_diameter']:
            try:
                row['permit_exhaust_diameter'] = float(row['permit_exhaust_diameter'])
                row['eia_exhaust_diameter'] = float(row['eia_exhaust_diameter'])
            except:
                logger.error(f'uscc: {row["uscc"]} permit_exhaust_diameter: {row["permit_exhaust_diameter"]} '
                             f'eia_exhaust_diameter: {row["eia_exhaust_diameter"]} 内径数据转换失败')
            else:
                if abs(row['permit_exhaust_diameter'] - row['eia_exhaust_diameter']) > 0.001:
                    mismatch.append('内径')

        # 经纬度校验
        if row['permit_exhaust_lon'] and row['permit_exhaust_lat'] and row['eia_exhaust_lon'] and row['eia_exhaust_lat']:
            try:
                row['permit_exhaust_lon'] = float(row['permit_exhaust_lon'])
                row['permit_exhaust_lat'] = float(row['permit_exhaust_lat'])
                row['eia_exhaust_lon'] = float(row['eia_exhaust_lon'])
                row['eia_exhaust_lat'] = float(row['eia_exhaust_lat'])
            except:
                logger.error(f'uscc: {row["uscc"]} permit_exhaust_lon: {row["permit_exhaust_lon"]}'
                             f'permit_exhaust_lat: {row["permit_exhaust_lat"]} eia_exhaust_lon: {row["eia_exhaust_lon"]}'
                             f'eia_exhaust_lat: {row["eia_exhaust_lat"]} 经纬度转换失败')
            else:
                if haversine_distance(
                    row['permit_exhaust_lat'], row['permit_exhaust_lon'], row['eia_exhaust_lat'], row['eia_exhaust_lon']
                ) > threshold:
                    mismatch.append('经纬度')

        if len(mismatch) > 0:
            row['reason'] = ','.join(mismatch)
            res.append(row.to_dict())

    res = pd.DataFrame(res).replace({float('nan'): None}).to_dict('records')
    return res


def check_absence_port(city):
    """ XKZ-04-1-环评报告中记载的污染物排放口未在许可证中全部体现 """
    hp_pw_map = get_data1(city)
    data = hp_pw_map[hp_pw_map['hp_outlet_code'].notnull() & hp_pw_map['pw_outlet_code'].isnull()]
    data = data[[
        'uscc', 'monitoring_type', 'hp_enterprise_id', 'hp_enterprise_name', 'hp_outlet_name', 'hp_outlet_code',
        'f_enterprise_id'
    ]]
    data = data.to_dict('records')
    return data


if __name__ == "__main__":
    data = check_absence_port('九龙坡区')
    # data = check_gas_port('九龙坡区')
    print(data)
