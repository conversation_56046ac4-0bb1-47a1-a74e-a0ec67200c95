# -*- coding: utf-8 -*-
"""
@File  : check_exec_online_monitor_quarter.py
@Author: <PERSON>
@Date  : 2025/8/5 10:34
@Desc  : 季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记不匹配
"""

import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
import traceback

from lib import logger
from lib.check.base import get_db_cursor

# 配置参数
FLAG_CODE = '1-2'  # 在线监测数据工况标记代码


def get_city_code(city: str) -> str:
    """
    根据城市名称获取城市编码

    Args:
        city (str): 城市名称

    Returns:
        str: 城市编码
    """
    db = get_db_cursor('main')
    sql = """
        SELECT code 
        FROM dim_ref_comm_country 
        WHERE value = '%s'
    """ % (city,)
    result = db.query_sql(sql)
    db.close()
    
    if not result.empty:
        return result.iloc[0]['code']
    else:
        raise ValueError(f"未找到城市 {city} 的编码")


def _get_quarter_from_date(date_obj: datetime) -> str:
    """
    根据日期计算季度字符串

    Args:
        date_obj (datetime): 日期对象

    Returns:
        str: 季度字符串，格式为 "YYYY年第N季"
    """
    year = date_obj.year
    month = date_obj.month
    quarter = (month - 1) // 3 + 1
    return f"{year}年第{quarter}季"


def check_exec_online_monitor_mismatch(start_time: str, end_time: str, city: str) -> List[Dict[str, Any]]:
    """
    检查季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记是否匹配

    Args:
        start_time (str): 查询开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
        end_time (str): 查询结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
        city (str): 城市名称

    Returns:
        List[Dict[str, Any]]: 不匹配的记录列表
    """
    try:
        # 获取城市编码
        city_code = get_city_code(city)
        logger.info(f"城市 {city} 的编码为: {city_code}")
    except ValueError as e:
        logger.error(f"获取城市编码失败: {e}")
        return []
    
    # 获取数据库连接
    db = get_db_cursor('main')
    
    try:
        # 动态生成表名
        from lib.check.table_name_generator import get_table_name
        abnormal_table = get_table_name("zxgl_basic_info_tool_abnormal_quart_report", city_code)

        # 查询执行报告季报异常记录 (表1)
        exec_report_sql = f"""
            SELECT
                f_enterprise_id,
                f_enterprise_name,
                report_time,
                bad_equip,
                over_std_start_time,
                over_std_end_time,
                bad_equip_id
            FROM {abnormal_table}
            WHERE region_code = '{city_code}'
                AND over_std_start_time >= '{start_time}'
                AND over_std_end_time <= '{end_time}'
        """
        exec_report_data = db.query_sql(exec_report_sql)
        logger.info(f"查询到 {len(exec_report_data)} 条执行报告异常记录")
        
        # 查询中间表数据 (表4)
        mapping_table = get_table_name("zx_basic_map_zx_map_jx_monitor_sit", city_code)
        mapping_sql = f"""
            SELECT
                f_enterprise_id,
                bad_equip_id,
                pollution_source_name,
                emission_id,
                bad_equip,
                emission_name
            FROM {mapping_table}
            WHERE region_code = '{city_code}'
        """
        mapping_data = db.query_sql(mapping_sql)
        mapping_data = mapping_data.drop_duplicates()
        logger.info(f"查询到 {len(mapping_data)} 条中间表映射记录")
        
        # 查询废气在线监测数据 (表2)
        air_monitor_sql = """
            SELECT 
                pollution_source_name,
                emission_id,
                monitoring_time,
                flag_code,
                emission_name AS monitor_equipment_name
            FROM data_emission_air_monitor_hour_summary
            WHERE flag_code = '%s'
                AND monitoring_time >= '%s'
                AND monitoring_time <= '%s'
        """ % (FLAG_CODE, start_time, end_time)
        air_monitor_data = db.query_sql(air_monitor_sql)
        logger.info(f"查询到 {len(air_monitor_data)} 条废气在线监测记录")
        
        # 查询废水在线监测数据 (表3)
        water_monitor_sql = """
            SELECT 
                pollution_source_name,
                emission_id,
                monitoring_time,
                flag_code,
                emission_name AS monitor_equipment_name
            FROM data_emission_water_monitor_hour_summary
            WHERE flag_code = '%s'
                AND monitoring_time >= '%s'
                AND monitoring_time <= '%s'
        """ % (FLAG_CODE, start_time, end_time)
        water_monitor_data = db.query_sql(water_monitor_sql)
        logger.info(f"查询到 {len(water_monitor_data)} 条废水在线监测记录")
        
        # 合并废气和废水监测数据
        air_monitor_data['pollution_type'] = 'gas'
        water_monitor_data['pollution_type'] = 'water'
        monitor_data = pd.concat([air_monitor_data, water_monitor_data], ignore_index=True)
        logger.info(f"合并后共有 {len(monitor_data)} 条在线监测记录")
        
        # 查找不匹配的记录
        mismatch_records = []
        
        # 创建映射字典，便于快速查找
        mapping_dict = {}  # 从(pollution_source_name, emission_id)到(f_enterprise_id, bad_equip_id)
        for _, row in mapping_data.iterrows():
            key = (row['pollution_source_name'], row['emission_id'])
            mapping_dict[key] = (row['f_enterprise_id'], row['bad_equip_id'])
        
        # 创建执行报告数据字典，便于快速查找
        exec_dict = {}  # 从(f_enterprise_id, bad_equip_id)到异常时间段列表
        for _, row in exec_report_data.iterrows():
            key = (row['f_enterprise_id'], row['bad_equip_id'])
            if key not in exec_dict:
                exec_dict[key] = []
            exec_dict[key].append({
                'start_time': row['over_std_start_time'],
                'end_time': row['over_std_end_time'],
                'f_enterprise_id': row['f_enterprise_id'],
                'f_enterprise_name': row['f_enterprise_name'],
                'report_time': row['report_time'],
                'bad_equip': row['bad_equip']
            })

        # 检查每条在线监测数据是否在执行报告的时间段内有对应记录
        for _, monitor_row in monitor_data.iterrows():
            pollution_source_name = monitor_row['pollution_source_name']
            emission_id = monitor_row['emission_id']
            monitoring_time = monitor_row['monitoring_time']
            pollution_type = monitor_row['pollution_type']

            # 通过中间表关联
            mapping_key = (pollution_source_name, emission_id)
            if mapping_key not in mapping_dict:
                # 如果在中间表中找不到映射关系，则认为不匹配
                mismatch_record = {
                    'report_time': _get_quarter_from_date(monitoring_time),
                    'pollution_type': pollution_type,
                    'pollution_source_name': pollution_source_name,
                    'monitor_equipment_name': monitor_row['monitor_equipment_name'],
                    'emission_id': emission_id,
                    'fault_time': monitoring_time,
                    'reason': '在中间表中找不到关联关系'
                }
                logger.warning(f"{mismatch_record}")

                continue

            f_enterprise_id, bad_equip_id = mapping_dict[mapping_key]
            exec_key = (f_enterprise_id, bad_equip_id)

            # 检查在指定时间是否在执行报告的异常时间范围内
            has_matching_exec = False
            matching_exec_record = None

            if exec_key in exec_dict:
                for exec_record in exec_dict[exec_key]:
                    start_time_exec = exec_record['start_time']
                    end_time_exec = exec_record['end_time']
                    # 检查监测时间是否在执行报告的异常时间范围内
                    if start_time_exec <= monitoring_time <= end_time_exec:
                        has_matching_exec = True
                        matching_exec_record = exec_record
                        break

            # 如果没有匹配的执行报告记录，则认为不匹配
            if not has_matching_exec:
                row = mapping_data[
                    (mapping_data['f_enterprise_id'] == f_enterprise_id) &
                    (mapping_data['bad_equip_id'] == bad_equip_id)
                ].iloc[0]
                mismatch_record = {
                    # 企业ID: 用于唯一标识企业的编码
                    'f_enterprise_id': f_enterprise_id,
                    # 企业名称: 出现异常数据的企业名称
                    'f_enterprise_name': row['f_enterprise_name'],
                    # 报告时间: 异常发生所在季度，格式为"YYYY年第N季"
                    'report_time': _get_quarter_from_date(monitoring_time),
                    # 污染类型: 数据类型，'gas'表示废气，'water'表示废水
                    'pollution_type': pollution_type,
                    # 异常设备ID: 出现异常的污染治理设备唯一标识
                    'bad_equip_id': bad_equip_id,
                    # 异常设备: 出现异常的污染治理设备名称
                    'bad_equip': row['bad_equip'],
                    # 超标开始时间: 执行报告中记录的超标时段开始时间
                    'over_std_start_time': '',
                    # 超标结束时间: 执行报告中记录的超标时段结束时间
                    'over_std_end_time': '',
                    # 监测设备名称: 出现异常的在线监测设备名称
                    'monitor_equipment_name': row['emission_name'],
                    # 故障时间: 在线监测数据中标记为异常的时间点
                    'fault_time': monitoring_time,
                    # 原因: 不匹配的原因说明
                    'reason': '在线监测异常数据在执行报告中无对应记录'
                }
                mismatch_records.append(mismatch_record)

        logger.info(f"共发现 {len(mismatch_records)} 条不匹配记录")
        return mismatch_records

    except Exception as e:
        logger.error(f"检查执行报告与在线监测数据匹配性时发生错误: {e}")
        logger.error(traceback.format_exc())
        return []
    finally:
        db.close()


if __name__ == "__main__":
    # 示例用法
    start_time = "2025-01-01 00:00:00"
    end_time = "2025-12-31 23:59:59"
    city = "九龙坡区"
    
    mismatch_results = check_exec_online_monitor_mismatch(start_time, end_time, city)
    
    if mismatch_results:
        print(f"发现 {len(mismatch_results)} 条不匹配记录:")
        print(mismatch_results)
    else:
        print("未发现不匹配记录或发生错误")