# -*- coding: utf-8 -*-
"""
@File  : check_oil.py
@Author: <PERSON>
@Date  : 2025/6/20 17:50
@Desc  : 加油站相关规则校验
"""
import warnings
from collections import Counter

from lib import logger, Global
from lib.check.base import timing_decorator, get_db_cursor, clean_null


def remove_common_elements(list1, list2):
    # 计算两个列表的频率
    count1 = Counter(list1)
    count2 = Counter(list2)

    # 找出共同元素（集合交集）
    common_elements = set(count1.keys()) & set(count2.keys())

    # 为每个共同元素计算 min_count（最小出现次数）
    min_count = {}
    for elem in common_elements:
        min_count[elem] = min(count1[elem], count2[elem])

    # 处理 list1：构建新列表并记录移除计数
    new_list1 = []
    removed_count1 = {elem: 0 for elem in common_elements}  # 初始化共同元素的移除计数

    for item in list1:
        if item in common_elements:
            if removed_count1[item] < min_count[item]:
                # 移除元素：不添加到新列表，增加移除计数
                removed_count1[item] += 1
            else:
                # 保留元素：添加到新列表
                new_list1.append(item)
        else:
            # 非共同元素，直接保留
            new_list1.append(item)

    # 处理 list2：类似 list1，但使用独立的移除计数
    new_list2 = []
    removed_count2 = {elem: 0 for elem in common_elements}

    for item in list2:
        if item in common_elements:
            if removed_count2[item] < min_count[item]:
                removed_count2[item] += 1
            else:
                new_list2.append(item)
        else:
            new_list2.append(item)

    return new_list1, new_list2


@timing_decorator
def get_data(city):
    logger.info(f'拉取{city}全部加油站许可证、环评数据...')
    if city in Global.GET_OIL_DATA:
        return Global.GET_OIL_DATA[city]
    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    permit_table = get_table_name("pw_basic_information_product_capacity", city)
    oil_tank_table = get_table_name("hp_basic_data_oil_tank", city)

    permit_sql = f"""
        select id, f_enterprise_name as enterprise_name, region_code, f_enterprise_id as enterprise_id, uscc, equip_name,
            equip_code, "parameter_name", measurement_unit, design_value
        from {permit_table}
        where
          f_enterprise_id in (
            select distinct f_enterprise_id from {oil_tank_table}
          )
          and main_project = '储罐区'
          and region_code in (
            select code from dim_ref_comm_country where value = '{city}'
          )
    """

    permit = db.query_sql(permit_sql)
    eia_sql = f"""
        select * from {oil_tank_table}
        where region_code in (
            select code from dim_ref_comm_country where value = '{city}'
        )
    """
    eia = db.query_sql(eia_sql)
    logger.info(f'拉取{city}全部加油站许可证、环评数据完成')
    Global.GET_OIL_DATA[city] = (permit, eia)
    return Global.GET_OIL_DATA[city]


def _pre_check(permit, eia):
    """
    对比环评和许可证数据中的油罐类型，筛选出关注的油罐类型。

    该函数首先定义了关注的油罐类型列表，然后检查环评和许可证数据中是否存在不在该列表中的油罐类型。
    如果发现新的油罐类型，将记录警告日志。最后，函数会过滤掉不在关注列表中的油罐类型，并返回过滤后的数据。

    参数:
    - permit: 许可证数据，DataFrame类型，包含装备名称等信息。
    - eia: 环评数据，DataFrame类型，包含油罐名称等信息。

    返回:
    - permit: 过滤后的许可证数据，仅包含关注油罐类型的记录。
    - eia: 过滤后的环评数据，仅包含关注油罐类型的记录。
    """
    # 定义关注的油罐类型列表
    _types = ['柴油储罐', '汽油储罐']

    # 检查环评数据中的油罐类型
    for _type in eia['oil_tank_name'].unique():
        if _type not in _types:
            logger.warning(f'环评加油站储罐新类型`{_type}`!')

    # 检查许可证数据中的油罐类型
    for _type in permit['equip_name'].unique():
        if _type not in _types:
            logger.warning(f'许可证加油站储罐新类型`{_type}`!')

    # 过滤许可证数据，仅保留关注的油罐类型
    permit = permit[permit['equip_name'].isin(_types)]

    # 过滤环评数据，仅保留关注的油罐类型
    eia = eia[eia['oil_tank_name'].isin(_types)]

    # 返回过滤后的许可证和环评数据
    return permit, eia


def check_quantity(permit, eia):
    res = {}
    permit, eia = _pre_check(permit, eia)
    same_ents = list(set(eia['f_enterprise_id'].unique()) & set(permit['enterprise_id'].unique()))
    for ent_id in same_ents:
        ent_name = permit[permit['enterprise_id'] == ent_id]['enterprise_name'].unique()[0]
        res[ent_id] = {'quantity_mismatch': [], 'only_eia': [], 'enterprise_name': ent_name}
        ent_permit = permit[permit['enterprise_id'] == ent_id]
        ent_eia = eia[eia['f_enterprise_id'] == ent_id]
        for (equip_name, equip_code), group in permit.groupby(['equip_name', 'equip_code']):
            # 许可证数量
            if '数量' not in group['parameter_name'].unique():
                logger.warning(f'{ent_id} {ent_name} {equip_name} {equip_code} 没有数量参数!')
                continue
            count = group[group['parameter_name'] == '数量']['design_value']
            if count.shape[0] > 1:
                logger.warning(f'{ent_id} {ent_name} {equip_name} {equip_code} 数量参数重复!')
                continue
            permit_count = int(count.iloc[0])

            # 环评数量
            eia_group =ent_eia[ent_eia['oil_tank_name'] == equip_name]
            if eia_group.shape[0] == 0:
                eia_count = 0
            else:
                eia_count = int(eia_group['oil_tank_count'].sum())

            # check
            if permit_count != eia_count:
                res[ent_id]['quantity_mismatch'].append({
                    'equip_name': equip_name,
                    'equip_code': equip_code,
                    'permit_count': permit_count,
                    'eia_count': eia_count
                })

        # 环评报告记载且许可证未记载储罐类型
        eia_type = ent_eia['oil_tank_name'].unique()
        permit_type = ent_permit['equip_name'].unique()
        for _type in eia_type:
            if _type not in permit_type:
                res[ent_id]['only_eia'].append(_type)
    return res


def check_volume(permit, eia):
    res = []
    permit, eia = _pre_check(permit, eia)
    same_ents = list(set(eia['f_enterprise_id'].unique()) & set(permit['enterprise_id'].unique()))
    for ent_id in same_ents:
        ent_name = permit[permit['enterprise_id'] == ent_id]['enterprise_name'].unique()[0]
        ent_permit = permit[permit['enterprise_id'] == ent_id]
        ent_eia = eia[eia['f_enterprise_id'] == ent_id]
        for (equip_name, equip_code), permit_group in permit.groupby(['equip_name', 'equip_code']):
            if equip_name not in ent_eia['oil_tank_name'].unique():
                continue
            ok = True
            for item in ['数量', '公称容积']:
                if item not in permit_group['parameter_name'].unique():
                    logger.warning(f'{ent_id} {ent_name} {equip_name} {equip_code} 没有{item}参数!')
                    ok = False
            if not ok:
                continue
            count = permit_group[permit_group['parameter_name'] == '数量']['design_value']
            if count.shape[0] > 1:
                logger.warning(f'{ent_id} {ent_name} {equip_name} {equip_code} 数量参数重复!')
                continue
            permit_count = int(count.iloc[0])
            eia_count = ent_eia[ent_eia['oil_tank_name'] == equip_name]['oil_tank_count']
            if permit_count != eia_count.sum():
                continue

            # 数量相同
            permit_vol = permit_group[permit_group['parameter_name'] == '公称容积']['design_value']
            if permit_vol.shape[0] > 1:
                logger.warning(f'{ent_id} {ent_name} {equip_name} {equip_code} 公称容积参数重复!')
                continue
            permit_vol = [int(permit_vol.iloc[0])] * int(permit_count)
            eia_vol = ent_eia[ent_eia['oil_tank_name'] == equip_name]['oil_tank_volume'].astype(int).to_list()
            eia_vol = [vol for vol, count in zip(eia_vol, eia_count) for i in range(count)]
            permit_vol, eia_vol = sorted(permit_vol), sorted(eia_vol)
            permit_vol, eia_vol = remove_common_elements(permit_vol, eia_vol)
            if permit_vol != eia_vol:
                res.append({
                    'enterprise_id': ent_id,
                    'enterprise_name': ent_name,
                    'equip_name': equip_name,
                    'equip_code': equip_code,
                    'permit_volume': permit_vol,
                    'eia_volume': eia_vol,
                    'different': permit_vol != eia_vol
                })
    res = clean_null(res)
    return  res


if __name__ == '__main__':
    permit, eia = get_data('九龙坡区')
    res = check_quantity(permit, eia)
    # res = check_volume(permit, eia)
    print(res)

