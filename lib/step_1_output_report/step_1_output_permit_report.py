"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-18 5:42:11 PM


  @ FilePath:  -> srv-jiulongpo-word-check -> lib -> step_1_output_report -> step_1_output_bed.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-
import os
import re
import sys
import json
import requests
import pandas as pd

from typing import Union
from thefuzz import process
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor, ALL_COMPLETED, wait

sys.path.append('../../')

from lib import  logger, license_number, proj_dir, DOCX_UPLOA_URL
from lib.step_1_output_report.report_generator import ReportGenerator

def is_valid(s):
    return not bool(re.fullmatch(r'^[A-Za-z0-9_]*$', s))


class MyReportGenerator(ReportGenerator):
    
    # 初始化
    def __init__(self, output_path, data, company_id, company_name) -> None:
        """
        初始化ReportGenerator对象
        @param output_path: 输出文件路径
        @param json_info: json数据
        @param company: 公司名称
        @param licence_number: 排污许可证编号
        """
        super().__init__(output_path)

        self.data = data
        self.company_id = company_id
        self.company = company_name
        
    def add_formatted_title(self, title_text: SyntaxError, font_size=16, bold=False, align='LEFT', p_before=0, p_after=0, style=None):
        """
        添加格式化标题到文档
        :param doc: Word文档对象
        :param title_text: 标题文本（支持中英混合）
        :param font_size: 字号（磅）16:文字号三号对应16磅
        """

        # 创建居中对齐的段落
        paragraph = self.doc.add_paragraph(text=title_text)

        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.__dict__.get(align)
        paragraph.paragraph_format.space_before = Pt(p_before)
        paragraph.paragraph_format.space_after = Pt(p_after)
        paragraph.paragraph_format.line_spacing = 1.5
        if style:
            paragraph.style = style

        for run in paragraph.runs:
            run.font.name = '仿宋'
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋')
            # 统一设置字号和加粗
            run.font.size = Pt(font_size)           # 中
            run.font.bold = bold
            run.font.color.rgb = RGBColor(0, 0, 0)
            run.font.underline = False

    def generate(self):
        
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("许可证-一般环境问题", 14, True, 'RIGHT')
        
        # 医疗机构病床数与卫健委数据不符
        # self.generate_question_1()

        # oil
        self.generate_question_quantity()
        self.generate_question_volume()

        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_question_quantity(self):
        oil_quantity = self.data['oil_quantity']
        if not (
            self.company_id in oil_quantity and
            (oil_quantity[self.company_id]['quantity_mismatch'] or oil_quantity[self.company_id]['only_eia'])
        ):
             return

        oil_quantity = oil_quantity[self.company_id]
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-06-1-加油站油罐个数与环境影响评价报告不符', 14, True, style='Heading 1')
        title = [
            ['企业名称', '生产设施名称', '许可证数量', '环评数量', '环评报告记载且许可证未记载储罐类型']
        ]
        table = self.create_table_with_merges(title)
        if 'quantity_mismatch' in oil_quantity:
            for row in oil_quantity['quantity_mismatch']:
                self.add_row(
                    table,
                    ([self.company, row['equip_name'], row['permit_count'], row['eia_count'], '']),
                    'Normal'
                )
        if 'only_eia' in oil_quantity and oil_quantity['only_eia']:
            self.add_row(
                table,
                (self.company, '', '', '', '、'.join(oil_quantity['only_eia'])),
                'Normal'
            )
        self.add_formatted_title('', 12)

    def generate_question_volume(self):
        def create_table():
            return self.add_specific_table(1, headers=['企业名称', '生产设施名称', '公秤容积_许可证', '公称容积_环评'])

        oil_volume = self.data['oil_volume']
        if not self.company in oil_volume['enterprise_name'].to_list():
            return

        oil_volume = oil_volume[oil_volume['enterprise_name'] == self.company]
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-06-2-加油站油罐容积与环境影响评价报告不符', 14, True,
                                 style='Heading 1')
        table = create_table()
        for _, row in oil_volume.iterrows():
            self.add_row(
                table,
                ([self.company, row['equip_name'], row['permit_volume'], row['eia_volume']]),
                'Normal'
            )
        self.add_formatted_title('', 12)

    def generate_question_1(self):
        # 医疗机构病床数与卫健委数据不符

        def create_table():
            return self.add_specific_table(2, headers=["企业名称", "排污许可证编号", "许可证", "卫健委"], 
                                           merged_header={"text": "床位数", "cols": [2, 3]})

        
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、许可证记载信息与第三方数据不符', 14, True, style='Heading 1')
        self.get_number(2, style='一', increase=True)
        self.add_formatted_title(f'{self.number[2][0]}） 医疗机构病床数与卫健委数据不符', 12)

        table = create_table()
        for company_info in self.company_data:
            self.add_row(table, [self.company, self.licence_number, 
                                company_info['许可证床位数'], company_info['卫健委床位数']], 'Normal')
            self.add_formatted_title('', 12)

def get_ents_licence_number(company_name: str) -> str:

    if license_number.get(company_name, '') == '':
        return _fuzzy_match(company_name, license_number)
    else:
        return license_number[company_name], company_name


def _fuzzy_match(name, dict_name, threshold=85):
    """模糊匹配企业名称"""

    match = process.extractOne(name, list(dict_name.keys()))
    if match[1] >= threshold:
        # 返回license_number, 企业标准名称name
        return dict_name[match[0]], match[0]
    else:
        logger.warning(f'模糊匹配企业名称失败: {name}')
        return '', name


def create_permit_instance(fp, data, company_id, company_name):
    """创建类实例，并生成docx报告"""
    report = MyReportGenerator(fp, data, company_id, company_name)
    report.generate()


def batch_generate_reports_permit(data, job_id, use_mp = False, rerun=False):
    """批量生产docx"""
    files_path = []
    file_dir = f'{proj_dir}/data/jobs/{job_id}'
    os.makedirs(file_dir, exist_ok=True)

    oil_quantity = data['data']['json_oil_quantity']
    oil_volume = data['data']['json_oil_volume']
    oil_volume = pd.DataFrame(oil_volume)

    if  oil_volume.empty:
        id_name = {}
    else:
        id_name = dict(zip(oil_volume['enterprise_id'], oil_volume['enterprise_name']))
    for company_id in oil_quantity:
        id_name[company_id] = oil_quantity[company_id]['enterprise_name']

    # bed_company_names = [_['单位名称'] for _ in data['data']['json_bed']]

    data1 = {
        'oil_quantity': oil_quantity,
        'oil_volume': oil_volume
    }

    if use_mp:
        with Pool(processes = min(8, os.cpu_count())) as pool:
            for company_id, company_name in id_name.items():
                if not is_valid(company_name):
                    continue
                _, name_ = get_ents_licence_number(company_name)
                fp = f'{proj_dir}/data/jobs/{job_id}/XKJ许可证-一般环境问题-{name_}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name])
                else:
                    pool.apply_async(create_permit_instance, (fp, data1, company_id, company_name))
                    files_path.append([fp, company_name])
            pool.close()
            pool.join()
    else:
        for company_id, company_name in id_name.items():
            if not is_valid(company_name):
                continue
            _, name_ = get_ents_licence_number(company_name)
            fp = f'{proj_dir}/data/jobs/{job_id}/XKJ许可证-一般环境问题-{name_}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name])
            else:
                create_permit_instance(fp, data1, company_id, company_name)
                files_path.append([fp, company_name])
    return files_path


def upload_file(fp, company_name):
    """上传文件"""
    url = f'{DOCX_UPLOA_URL}'
    assert os.path.isfile(fp), f'to uploaed file not existd -> {fp}'
    with open(fp, 'rb') as f1:
        file_name = os.path.basename(fp)
        files = [('file', (file_name, f1, 'application/wps-office.docx'))]
        response = requests.post(url, files = files)
    assert response.status_code < 400, f'upload file error, status code -> {response.status_code}'
    info = response.json()['data']
    info['ent_name'] = company_name
    return info


def batch_or_single_upload_permit(files_path, use_mt = True):
    """使用多线程或者循环上传文件"""
    # logger.info('==> uploading docx files...')
    if use_mt:
        with ThreadPoolExecutor(max_workers = 16) as t:
            all_task = [t.submit(upload_file, fp, company_name) for fp, company_name in files_path]
            wait(all_task, return_when = ALL_COMPLETED)
        info_list = [task.result() for task in all_task]
    else:
        info_list = []
        for fp, company_name in files_path:
            info = upload_file(fp, company_name)
            info_list.append(info)
    return info_list


if __name__ == "__main__":
    # 读取JSON数据
    json_path = f'{proj_dir}/data/jobs/start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0/result_bed.json'
    with open(json_path, 'r', encoding='utf-8') as f:
        data = {'data': json.load(f)}

    # company_names = [_['单位名称'] for _ in data['data']]
    # for name in company_names:
    #     lic_, name_ = _fuzzy_match(name, license_number)
    #     os.makedirs(f'{proj_dir}/data/bed/', exist_ok=True)
    #     report = MyReportGenerator(f'{proj_dir}/data/{name_}_bed.docx', data, name, license_number)
    #     report.generate()

    print(batch_or_single_upload_permit(batch_generate_reports_permit(data,
        'start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0', False), False))