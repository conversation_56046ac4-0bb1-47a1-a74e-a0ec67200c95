"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-19 10:21:16 AM

  @ FilePath:  -> srv-jiulongpo-word-check -> lib -> step_1_output_report -> step_2_output_executive-report.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-

import os
import re
import sys
import json
import requests
import pandas as pd

from typing import Union
from thefuzz import process
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor, ALL_COMPLETED, wait

sys.path.append('../../')

from lib import  logger, license_number, proj_dir, DOCX_UPLOA_URL
from lib.step_1_output_report.report_generator import ReportGenerator


def is_valid(s):
    return not bool(re.fullmatch(r'^[A-Za-z0-9_]*$', s))


class MyReportGenerator(ReportGenerator):
    
    # 初始化
    def __init__(self, output_path: str, data: dict,
                 company: str, company_id: str) -> None:
        """
        初始化ReportGenerator对象
        @param output_path: 输出文件路径
        @param json_info: json数据
        @param json_mismatch: json数据
        @param info_anomaly: dict数据
        @param info_mismatch: dict数据
        @param company: 公司名称
        """
        super().__init__(output_path)

        self.data = data
        self.company = company
        self.company_id = company_id

        self.map = {'month': '月报', 'quarter': '季报', 'year': '年报',
                    'gas': '废气', 'water': '废水', 'unorgnized': '无组织',
                    }
    @staticmethod
    def load_data(data):
        """
        加载数据
        """
        if isinstance(data, str):
            with open(data, 'r', encoding='utf-8') as f:
                return json.load(f)['data']
        elif isinstance(data, dict):
            return data['data']

    def add_formatted_title(self, title_text: SyntaxError, font_size=16, bold=False, align='LEFT', p_before=0, p_after=0, style=None):
        """
        添加格式化标题到文档
        :param doc: Word文档对象
        :param title_text: 标题文本（支持中英混合）
        :param font_size: 字号（磅）16:文字号三号对应16磅
        """

        # 创建居中对齐的段落
        paragraph = self.doc.add_paragraph(text=title_text)

        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.__dict__.get(align)
        paragraph.paragraph_format.space_before = Pt(p_before)
        paragraph.paragraph_format.space_after = Pt(p_after)
        paragraph.paragraph_format.line_spacing = 1.5
        if style:
            paragraph.style = style

        for run in paragraph.runs:
            run.font.name = '仿宋'
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋')
            # 统一设置字号和加粗
            run.font.size = Pt(font_size)           # 中
            run.font.bold = bold
            run.font.color.rgb = RGBColor(0, 0, 0)
            run.font.underline = False

    def generate_exe_law(self):
        
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("执行报告-一般环境问题", 14, True, 'RIGHT')

        # 超过许可排放量排放污染物
        self.generate_question_2()

        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_exe_illegal(self):
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("执行报告-涉嫌违法行为", 14, True, 'RIGHT')

        # 企业未按规定正常提交执行报告
        self.generate_question_1()
        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_exe_illegal6(self):
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("执行报告-涉嫌违法行为", 14, True, 'RIGHT')

        # 6-1-年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过排污许可证许可排放量
        self.generate_question_6_1()
        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_exe_illegal2(self):
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("执行报告-涉嫌违法行为", 14, True, 'RIGHT')

        # 2-1年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过建设项目环境影响评价规定的总量控制指标
        self.generate_question_2_1()
        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def get_map(self, data):
        if data == 'water':
            return '废水'
        elif data == 'gas' or data == 'air':
            return '废气'
        else:
            return ''

    def generate_question_2_1(self):
        """ 年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过建设项目环境影响评价规定的总量控制指标 """
        if 'json_year_emission_hp' not in self.data.keys():
            return

        json_year_emission_hp = self.data['json_year_emission_hp']
        abnormal = [
            item
            for item in json_year_emission_hp
            if str(item['f_enterprise_id']) == self.company_id
        ]
        if not abnormal:
            return
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG02-1年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过建设项目环境影响评价规定的总量控制指标',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '报告时间', '污染物名称', '执行报告全厂年度实际排放量', '环评项目建成后全厂排放量']
        ]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            self.add_row(
                table,
                [
                    self.company, item['report_year'], item['pollutant_name'], item['actual_emission'], item['control_limit']
                ]
            )
        self.add_formatted_title('', 12)


    def generate_question_6_1(self):
        """ 年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过排污许可证许可排放量 """
        if 'json_mismatch' not in self.data.keys():
            return

        json_mismatch = self.data['json_mismatch']
        abnormal = [
            item
            for item in json_mismatch
            if str(item['f_enterprise_id']) == self.company_id
        ]
        if not abnormal:
            return
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG06-1年度执行报告“实际排放情况及达标判定分析”中记载的污染物年排放量超过排污许可证许可排放量',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '报告时间', '排口类型', '污染物名称', '实际排放量', '排污许可证排放量']
        ]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            self.add_row(
                table,
                [
                    self.company, item['year'], item['portType'], item['pollutantName'], item['exe_val'], item['licence_val']
                ]
            )
        self.add_formatted_title('', 12)


    def generate_question_9_1(self):
        """ 执行报告超标排放量信息表中污染物排污浓度与在线监测数据中超标监测指标/超标时段/超标数据不匹配 """
        if 'json_exe_online_exceeding' not in self.data.keys():
            return
        json_exe_online_exceeding = self.data['json_exe_online_exceeding']
        abnormal = [
            item
            for item in json_exe_online_exceeding
            if str(item['exec_enterprise_info']['f_enterprise_id']) == self.company_id
        ]
        if not abnormal:
            return

        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG09-1执行报告超标排放量信息表中污染物排污浓度与在线监测数据中超标监测指标/超标时段/超标数据不匹配',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '报告时间', '污染类型', '执行报告', '<left>', '<left>', '<left>', '<left>', '在线监测', '<left>', '<left>', '<left>'],
            ['<up>', '<up>', '<up>', '超标开始时间', '超标结束时间', '排口名称', '污染物名称', '实际排放浓度', '超标时间', '排口名称', '污染物名称', '实际排放浓度']
        ]
        table = self.create_table_with_merges(title)

        for item in abnormal:
            company = self.company
            report_time = item['report_time']
            pollution_type = self.get_map(item['data_type'])
            exe_port = item['exec_enterprise_info']['outlet_name']
            online_port = item['online_enterprise_info']['emission_name']
            online_time = item['monitoring_time'] if pollution_type == '废气' else item['monitoring_date']
            for row in item['pollutant_issues']:
                if row['missing_exec_record']:
                    self.add_row(
                        table,
                        [
                            company, report_time, pollution_type, '', '', str(exe_port), '', '', str(online_time),
                            str(online_port), str(row['pollution_name']), str(row['monitoring_value'] if pollution_type == '废气' else row['daily_avg_value'])
                        ],
                        'Normal'
                    )
                elif row['concentration_mismatch']:
                    self.add_row(
                        table,
                        [
                            company, report_time, pollution_type, str(row['concentration_mismatch']['exec_record']['over_std_start_time']),
                            str(row['concentration_mismatch']['exec_record']['over_std_end_time']), str(exe_port), str(row['excess_monitoring_type']),
                            str(row['concentration_mismatch']['exec_record']['actual_emission_con']), str(online_time), str(online_port), str(row['pollution_name']),
                            str(row['monitoring_value'] if pollution_type == '废气' else row['daily_avg_value'])
                        ],
                        'Normal'
                    )
                elif row['time_mismatch']:
                    for exec_record in row['time_mismatch']['exec_records']:
                        self.add_row(
                            table,
                            [
                                company, report_time, pollution_type, str(exec_record['over_std_start_time']),
                                str(exec_record['over_std_end_time']), str(exe_port), str(row['excess_monitoring_type']),
                                '', str(online_time), str(online_port), str(row['pollution_name']),
                                str(row['monitoring_value'] if pollution_type == '废气' else row['daily_avg_value'])
                            ],
                            'Normal'
                        )
        self.add_formatted_title('', 12)
        return True

    def generate_exe_total(self):

        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("执行报告-一般环境问题", 14, True, 'RIGHT')

        # 距离要求提交排污许可证执行报告截止日期较近【预警】
        self.generate_question_warning()

        # 月度/季度/年度执行报告排放量数据不匹配
        self.generate_question_3()

        # 执行报告记载的污染物排放量数据恒值
        # 问题4-1: 排放表数据恒值
        flag = self.generate_question_4_1()
        # 问题4-2: 单项污染物排放数据恒值
        flag = self.generate_question_4_2(True)

        # 9-1执行报告超标排放量信息表中污染物排污浓度与在线监测数据中超标监测指标/超标时段/超标数据不匹配
        self.generate_question_9_1()

        # 8-1季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记不匹配
        self.generate_question_8_1()

        # 7-1年度执行报告记载的自行监测执行情况与自行监测数据有效监测数据数量/最大值/最小值/平均值不匹配
        self.generate_question_7_1()

        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_question_7_1(self):
        """ 年度执行报告记载的自行监测执行情况与自行监测数据有效监测数据数量/最大值/最小值/平均值不匹配 """
        if 'json_exe_self_monitor' not in self.data.keys():
            return
        json_exe_self_monitor = self.data['json_exe_self_monitor']
        abnormal = [
            item
            for item in json_exe_self_monitor
            if str(item['f_enterprise_id']) == self.company_id
        ]
        if not abnormal:
            return

        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG07-1年度执行报告记载的自行监测执行情况与自行监测数据有效监测数据数量/最大值/最小值/平均值不匹配',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '报告时间', '污染类型', '排口名称', '排口类型', '污染物名称', '有效数据量', '<left>', '监测数据最大值', '<left>',  '监测数据最小值', '<left>', '监测数据平均值', '<left>'],
            ['<up>', '<up>', '<up>', '<up>', '<up>', '<up>', '执行报告', '自行监测', '执行报告', '自行监测', '执行报告', '自行监测', '执行报告', '自行监测']
        ]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            company = self.company
            report_time = item['report_year']
            pollution_type = self.get_map(item['pollutant_type'])
            port_name = item['outlet_id']
            port_type = item['monitoring_site_data']
            pollutant_name = item['monitoring_indicator_data']
            if item['count']:
                count_exe = item['count']['exec_value']
                count_self = item['count']['self_value']
            else:
                count_exe = ''
                count_self = ''
            if item['max']:
                max_exe = item['max']['exec_value']
                max_self = item['max']['self_value']
            else:
                max_exe = ''
                max_self = ''
            if item['min']:
                min_exe = item['min']['exec_value']
                min_self = item['min']['self_value']
            else:
                min_exe = ''
                min_self = ''
            if item['avg']:
                avg_exe = item['avg']['exec_value']
                avg_self = item['avg']['self_value']
            else:
                avg_exe = ''
                avg_self = ''
            self.add_row(
                table,
                [
                    company, report_time, pollution_type, port_name, port_type, pollutant_name, count_exe, count_self,
                    max_exe, max_self, min_exe, min_self, avg_exe, avg_self
                ],
                'Normal'
            )
            self.add_formatted_title('', 12)
            return True


    def generate_question_8_1(self):
        """"""
        if 'json_exe_online_monitor' not in self.data.keys():
            return

        json_exe_online_monitor = self.data['json_exe_online_monitor']
        abnormal = [
            item
            for item in json_exe_online_monitor
            if str(item['f_enterprise_id']) == self.company_id
        ]
        if not abnormal:
            return

        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG08-1季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记不匹配',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '报告时间', '污染类型', '故障设施名称', '超标开始时间', '超标结束时间', '在线监测工况设备名称', '故障时间']
        ]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            self.add_row(
                table,
                [
                    self.company, item['report_time'], self.get_map(item['pollution_type']), item['bad_equip'],
                    item['over_std_start_time'], item['over_std_end_time'], item['monitor_equipment_name'],
                    item['fault_time']
                ],
                'Normal'
            )
        self.add_formatted_title('', 12)
        return True


    def generate_question_warning(self):
        """ 距离要求提交排污许可证执行报告截止日期较近【预警】 """
        if 'json_warning' not in self.data.keys():
            return

        key = f"{self.company_id}({self.company})"
        json_waring = self.data['json_warning']
        if key not in json_waring.keys():
            return

        tmp_data = json_waring[key]
        for _ in tmp_data.keys():
            if _ == 'year':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-4距离要求提交排污许可证年度执行报告截止日期较近【预警】',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])

                for year in tmp_data[_]:
                    self.add_row(table, [self.company, '年报', year, year], 'Normal')
            elif _ == 'quarter':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-5距离要求提交排污许可证季度执行报告截止日期较近【预警】',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])
                for quarter in tmp_data[_]:
                    self.add_row(table, [self.company, '季报', quarter[0], f'Q{quarter[1]}'], 'Normal')
            elif _ == 'month':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-6距离要求提交排污许可证月度执行报告截止日期较近【预警】',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])
                for month in tmp_data[_]:
                    self.add_row(table, [self.company, '月报', month[0], month[1]], 'Normal')

            self.add_formatted_title('', 12)


    def generate_question_1(self):
        # 企业未按规定正常提交执行报告 
        def create_table():
            return self.add_specific_table(1, headers=["企业名称", "未提交报告类型", "年份", "报告时间"])

        if 'json_anomaly' not in self.data.keys():
            return
        json_anomaly = self.data['json_anomaly']
        key = f"{self.company_id}({self.company})"

        if key not in json_anomaly['check_yqm_absence'].keys():
            return False

        # if self.company not in self.info_anomaly.keys():
        #     return
        #
        # if self.info_anomaly[self.company][0] not in self.json_anomaly['check_yqm_absence'].keys():
        #     return
             
        # self.get_number(1, style='一', increase=True)
        # self.add_formatted_title(f'{self.number[1][1]}、企业未按规定正常提交执行报告', 14, True, style='Heading 1')
        
        # table = create_table()
        tmp_data = json_anomaly['check_yqm_absence'][key]
        for _ in tmp_data.keys():
            if _ == 'year':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-1未按照排污许可证规定提交排污许可证年度执行报告',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])

                for year in tmp_data[_]:
                    self.add_row(table, [self.company, '年报', year, year], 'Normal')
            elif _ == 'quarter':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-2未按照排污许可证规定提交排污许可证季度执行报告',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])
                for quarter in tmp_data[_]:
                    self.add_row(table, [self.company, '季报', quarter[0], f'Q{quarter[1]}'], 'Normal')
            elif _ == 'month':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(f'{self.number[1][1]}、ZXBG01-3未按照排污许可证规定提交排污许可证月度执行报告',
                                         14, True, style='Heading 1')
                table = self.add_specific_table(1, headers=["企业名称", "报告类型", "年份", "报告时间"])
                for month in tmp_data[_]:
                    self.add_row(table, [self.company, '月报', month[0], month[1]], 'Normal')
        
            self.add_formatted_title('', 12)


    def generate_question_2(self):
        # 超过许可排放量排放污染物 
        def create_table():
            return self.add_specific_table(1, headers=["报告年份", "排放口类型", "污染物名称", "实际排放量（吨）", "许可排放量（吨）"])


        if self.company not in self.info_mismatch.keys():
            return
        
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、超过许可排放量排放污染物', 14, True, style='Heading 1')

        table = create_table()
        for _ in self.json_mismatch:
            if _['ent_name'] == self.info_mismatch[self.company][0]:
                 self.add_row(table, [_['year'], _['portType'], _['pollutantName'], _['exe_val'], _['licence_val']], 'Normal')
        
        self.add_formatted_title('', 12)

    def generate_question_3(self):
        # 月度/季度/年度执行报告排放量数据不匹配
        def create_table():
            return self.add_specific_table(1, headers=["污染类型", "排放口类型", "排放方式", "污染物名称", 
                                                       "时间", "报告一类型", "报告一排放量(吨)", "报告二类型", "报告二排放量(吨)"])

        if 'json_anomaly' not in self.data.keys():
            return False

        json_anomaly = self.data['json_anomaly']
        key = f"{self.company_id}({self.company})"

        if key not in json_anomaly['check_pollution_emission_mismatch'].keys():
            return False

        def get_val(row, name):
            return row.get(name, '')

        tmp_data = json_anomaly['check_pollution_emission_mismatch'][key]
        for _type in tmp_data.keys():
            if _type == 'quarter-year':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(
                    f'{self.number[1][1]}、ZXBG03-3季度执行报告记载的污染物排放量数据与年度执行报告中记载的同季污染物排放量数据不匹配',
                                14, True, style='Heading 1'
                )
                title = [
                    ['企业名称', '污染类型', '排口类型', '排放类型', '排口名称', '污染物名称', '季报', '<left>', '年报', '<left>'],
                    ['<up>', '<up>', '<up>', '<up>', '<up>', '<up>', '时间', '排放量', '时间', '排放量']
                ]
                table = self.create_table_with_merges(title)
                for pollu_type in tmp_data[_type].keys():
                    if pollu_type == 'gas':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废气', get_val(row, 'portType'), '', '',
                                get_val(row, 'pollutantName'), row['time'], row['quarter'], row['time'], row['year']),
                                'Normal'
                            )
                    elif pollu_type == 'water':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废水', get_val(row, 'portType'), get_val(row, 'emissionType'), '',
                                get_val(row, 'pollutantName'), row['time'], row['quarter'], row['time'], row['year']),
                                'Normal'
                            )
            elif _type == 'month-quarter':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(
                    f'{self.number[1][1]}、ZXBG03-2月度执行报告记载的污染物排放量数据与季度执行报告中记载的同月污染物排放量数据不匹配',
                                14, True, style='Heading 1'
                )
                title = [
                    ['企业名称', '污染类型', '排口类型', '排放类型', '排口名称', '污染物名称', '月报', '<left>', '季报', '<left>'],
                    ['<up>', '<up>', '<up>', '<up>', '<up>', '<up>', '时间', '排放量', '时间', '排放量']
                ]
                table = self.create_table_with_merges(title)
                for pollu_type in tmp_data[_type].keys():
                    if pollu_type == 'gas':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废气', get_val(row, 'portType'), '', '',
                                get_val(row, 'pollutantName'), row['time'], row['month'], row['time'], row['quarter']),
                                'Normal'
                            )
                    elif pollu_type == 'water':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废水', get_val(row, 'portType'), get_val(row, 'emissionType'), '',
                                get_val(row, 'pollutantName'), row['time'], row['month'], row['time'], row['quarter']),
                                'Normal'
                            )
            elif _type == 'month-year':
                self.get_number(1, style='一', increase=True)
                self.add_formatted_title(
                    f'{self.number[1][1]}、ZXBG03-1年执行报告记载的污染物排放量数据与月度执行报告中记载的同月污染物排放量数据不匹配',
                                14, True, style='Heading 1'
                )
                title = [
                    ['企业名称', '污染类型', '排口类型', '排放类型', '排口名称', '污染物名称', '月报', '<left>', '年报', '<left>'],
                    ['<up>', '<up>', '<up>', '<up>', '<up>', '<up>', '时间', '排放量', '时间', '排放量']
                ]
                table = self.create_table_with_merges(title)
                for pollu_type in tmp_data[_type].keys():
                    if pollu_type == 'gas':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废气', get_val(row, 'portType'), '', '',
                                get_val(row, 'pollutantName'), row['time'], row['month'], row['time'], row['year']),
                                'Normal'
                            )
                    elif pollu_type == 'water':
                        for row in tmp_data[_type][pollu_type]:
                            self.add_row(
                                table,
                                (self.company, '废水', get_val(row, 'portType'), get_val(row, 'emissionType'), '',
                                get_val(row, 'pollutantName'), row['time'], row['month'], row['time'], row['year']),
                                'Normal'
                            )

            self.add_formatted_title('', 12)

    def generate_question_4_1(self):
        # 排放表数据恒值
        def create_table():
            return self.add_specific_table(1, headers=["企业名称", "恒值排放表名称", "报告1时间", "报告2时间"])

        if 'json_anomaly' not in self.data.keys():
            return False

        json_anomaly = self.data['json_anomaly']
        key = f"{self.company_id}({self.company})"

        if key not in json_anomaly['check_constant_value_all'].keys():
            return False


        tmp_data = json_anomaly['check_constant_value_all'][key]
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、ZXBG04-1当前执行报告与历史同周期类型（月/季/年）执行报告污染物排放数据完全重复',
                                 14, True, style='Heading 1')
        title = [
            ['企业名称', '当前执行报告', '<left>', '<left>', '历史同周期执行报告', '<left>', '<left>', '表名'],
            ['<up>', '类型', '年份', '时间', '类型', '年份', '时间', '<up>']
        ]
        table = self.create_table_with_merges(title)

        def get_type(txt):
            if '月' in txt:
                return '月报'
            elif '季' in txt:
                return '季报'
            elif '年' in txt:
                return '年报'
            else:
                return ''

        for pollu_type in tmp_data.keys():
            for row in tmp_data[pollu_type]:
                self.add_row(
                    table,
                    (
                        self.company, get_type(row['current_report_time']), row['current_report_time'][:5], row['current_report_time'],
                        get_type(row['last_report_time']), row['last_report_time'][:5], row['last_report_time'], self.map[pollu_type]
                     ),
                    'Normal'
                )
                
        self.add_formatted_title('', 12)
        return True
    
    def generate_question_4_2(self, flag):
        # 单项污染物排放数据恒值
        def create_table():
            return self.add_specific_table(1, headers=["企业名称", "排放表名称", "报告1时间", 
                                                       "报告2时间", "恒值定位", "恒值排放量（吨）" ])
        if 'json_anomaly' not in self.data.keys():
            return False

        key = f"{self.company_id}({self.company})"
        json_anomaly = self.data['json_anomaly']
        
        if key not in json_anomaly['check_constant_value_one'].keys():
            return False

        tmp_data = json_anomaly['check_constant_value_one'][key]
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(
            f'{self.number[1][1]}、ZXBG04-2连续三个相同周期类型执行报告中实际排放量信息表某项污染物排放量完全一致且3个周期的污染物排放量数据均大于0.01吨/月',
            14, True, style='Heading 1')
        title = [
            ['企业名称', '报告1', '<left>', '报告2', '<left>', '报告3', '<left>', '表名', '污染物名称（报告坐标）', '污染物排放量'],
            ['<up>', '类型', '时间', '类型', '时间', '类型', '时间', '<up>', '<up>', '<up>']
        ]
        table = self.create_table_with_merges(title)

        def get_type(txt):
            if '月' in txt:
                return '月报'
            elif '季' in txt:
                return '季报'
            elif '年' in txt:
                return '年报'
            else:
                return ''

        for pollu_type in tmp_data.keys():
            for row in tmp_data[pollu_type]:
                self.add_row(
                    table,
                    (
                        self.company, get_type(row['last_last_report_time']), row['last_last_report_time'],
                        get_type(row['last_report_time']), row['last_report_time'],
                        get_type(row['current_report_time']), row['current_report_time'],
                        self.map[pollu_type], '-'.join([str(i) for i in row['location']]), row['value']
                    ),
                    'Normal'
                )
                
        self.add_formatted_title('', 12)
        return True

def get_ents_licence_number(company_name: str) -> str:

    if license_number.get(company_name, '') == '':
        return _fuzzy_match(company_name, license_number)
    else:
        return license_number[company_name], company_name


def _fuzzy_match(name, dict_name, threshold=85):
    """模糊匹配企业名称"""

    match = process.extractOne(name, list(dict_name.keys()))
    if match[1] >= threshold:
        # 返回license_number, 企业标准名称name
        return dict_name[match[0]], match[0]
    else:
        logger.debug(f'模糊匹配企业名称失败: {name}')
        return '', name

def preprocess_json(data):
    """
    提取企业id与企业名称
    """
    id_name = {}

    def extract_id_name(string):
        """从"646332376625221(重庆和友实业股份有限公司)"中提取id和name """
        id_ = string.split('(')[0]
        name_ = string.split('(')[1][:-1]
        return id_, name_

    for _type, group in data.items():
        if _type == 'json_anomaly':
            for key, val in group.items():
                for string in val.keys():
                    id_, name_ = extract_id_name(string)
                    id_name[id_] = name_
        elif _type == 'json_mismatch':
            for item in group:
                id_name[item['f_enterprise_id']] = item['ent_name']
        elif _type == 'json_warning':
            for key in group.keys():
                id_, name_ = extract_id_name(key)
                id_name[id_] = name_
        elif _type == 'json_exe_online_exceeding':
            for row in group:
                exe = row['exec_enterprise_info']
                id_name[exe['f_enterprise_id']] = exe['f_enterprise_name']
        elif _type in ['json_exec_online_monitor', 'json_exe_self_monitor', 'json_year_emission_hp']:
            for row in group:
                id_name[row['f_enterprise_id']] = row['f_enterprise_name']
        else:
            raise ValueError(f'未知的json类型: {_type}')
    return id_name


def create_exe_law_instance(fp, data_anomaly, data_mismatch, info_anomaly, info_mimatch, company_name):
    """创建类实例，并生成docx报告"""

    # lic_, name_ = _fuzzy_match(name, license_number)
        
    # report = MyReportGenerator(f'{proj_dir}/data/{name_}_anomaly.docx', 
    #                            data_1, data_2,
    #                            info_anomoly, info_mismatch,  
    #                            name)
    report = MyReportGenerator(fp, data_anomaly, data_mismatch, info_anomaly, info_mimatch, company_name)
    report.generate_exe_law()


def create_exe_total_instance(fp, data, company_name, company_id):
    """ZXBG执行报告-般环境问题.docx"""
    report = MyReportGenerator(fp, data, company_name, company_id)
    report.generate_exe_total()


def create_exe_illegal_instance(fp, data, company_name, company_id):
    """ZXBG01-1~3执行报告-涉嫌违法行为"""
    report = MyReportGenerator(fp, data, company_name, company_id)
    report.generate_exe_illegal()


def create_exe_illegal6_instance(fp, data, company_name, company_id):
    """ZXBG06-1执行报告-涉嫌违法行为"""
    report = MyReportGenerator(fp, data, company_name, company_id)
    report.generate_exe_illegal6()


def create_exe_illegal2_instance(fp, data, company_name, company_id):
    """ZXBG02-1执行报告-涉嫌违法行为"""
    report = MyReportGenerator(fp, data, company_name, company_id)
    report.generate_exe_illegal2()


def batch_generate_reports_anomaly(data, job_id, use_mp = True):
    """批量生产docx"""
    files_path = []
    file_dir = f'{proj_dir}/data/jobs/{job_id}'
    os.makedirs(file_dir, exist_ok=True)

    id_name = preprocess_json(data)
    name_id = {v: k for k, v in id_name.items()}

    if use_mp:
        with Pool(processes = min(8, os.cpu_count())) as pool:
            for company_name, company_id in name_id.items():
                # if not is_valid(company_name):
                #     continue
                fp = f'{file_dir}/ZXBG01-1~3执行报告-涉嫌违法行为-{company_name}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name, company_id])
                else:
                    files_path.append([fp, company_name, company_id])
                    pool.apply_async(
                        create_exe_illegal_instance,
                        (
                            fp, data,
                            company_name, company_id
                        )
                   )
                fp = f'{file_dir}/ZXBG06-1执行报告-涉嫌违法行为-{company_name}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name, company_id])
                else:
                    files_path.append([fp, company_name, company_id])
                    pool.apply_async(
                        create_exe_illegal6_instance,
                        (fp, data, company_name, company_id)
                    )
                fp = f'{file_dir}/ZXBG02-1执行报告-涉嫌违法行为-{company_name}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name, company_id])
                else:
                    files_path.append([fp, company_name, company_id])
                    pool.apply_async(
                        create_exe_illegal2_instance,
                        (fp, data, company_name, company_id)
                    )
                fp = f'{file_dir}/ZXBG执行报告-一般环境问题-{company_name}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name, company_id])
                else:
                    files_path.append([fp, company_name, company_id])
                    pool.apply_async(
                        create_exe_total_instance,
                        (fp, data, company_name, company_id)
                    )
            pool.close()
            pool.join()
    else:
        for company_name, company_id in name_id.items():
            # if not is_valid(company_name):
            #     continue

            fp = f'{file_dir}/ZXBG01-1~3执行报告-涉嫌违法行为-{company_name}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name, company_id])
            else:
                create_exe_illegal_instance(fp, data, company_name, company_id)
                files_path.append([fp, company_name, company_id])

            fp = f'{file_dir}/ZXBG06-1执行报告-涉嫌违法行为-{company_name}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name, company_id])
            else:
                create_exe_illegal6_instance(fp, data, company_name, company_id)
                files_path.append([fp, company_name, company_id])

            fp = f'{file_dir}/ZXBG02-1执行报告-涉嫌违法行为-{company_name}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name, company_id])
            else:
                create_exe_illegal2_instance(fp, data, company_name, company_id)
                files_path.append([fp, company_name, company_id])

            fp = f'{file_dir}/ZXBG执行报告-一般环境问题-{company_name}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name, company_id])
            else:
                create_exe_total_instance(fp, data, company_name, company_id)
                files_path.append([fp, company_name, company_id])

    return files_path


def upload_file(fp, company_name, company_id):
    """上传文件"""
    url = f'{DOCX_UPLOA_URL}'
    assert os.path.isfile(fp), f'to uploade file not existd -> {fp}'
    with open(fp, 'rb') as f1:
        file_name = os.path.basename(fp)
        files = [('file', (file_name, f1, 'application/wps-office.docx'))]
        response = requests.post(url, files = files)
    assert response.status_code < 400, f'upload file error, status code -> {response.status_code}'
    info = response.json()['data']
    # if '执法队' not in info['fileName']:
    #     url = info['url']
    #     url = url.split('//', 1)[-1]
    #     url = '/'.join(url.split('/')[1:])
    #     info['url'] = url
    info['ent_name'] = company_name
    info['f_enterprise_id'] = company_id
    return info

def batch_or_single_upload_anomaly(files_path, use_mt = True):
    """使用多线程或者循环上传文件"""
    # logger.info('==> uploading docx files...')
    if use_mt:
        with ThreadPoolExecutor(max_workers = 16) as t:
            all_task = [t.submit(upload_file, fp, company_name, company_id) for fp, company_name, company_id in files_path]
            wait(all_task, return_when = ALL_COMPLETED)
        info_list = [task.result() for task in all_task]
    else:
        info_list = []
        for fp, company_name, company_id in files_path:
            info = upload_file(fp, company_name, company_id)
            info_list.append(info)
    return info_list


if __name__ == "__main__":
    # 读取JSON数据
    json_path = f'{proj_dir}/data/jobs/start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0/result_anomaly.json'
    with open(json_path, 'r', encoding='utf-8') as f:
        data_1 = {'data': json.load(f)}

    json_path = f'{proj_dir}/data/jobs/start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0/result_year_emission.json'
    with open(json_path, 'r', encoding='utf-8') as f:
        data_2 = {'data': json.load(f)}

    # info_anomoly, info_mismatch = preprocess_json(data_1, data_2, license_number)

    # company_names = list(set(info_anomoly.keys()).union(info_mismatch.keys()))
    # for name in company_names:
    #     lic_, name_ = _fuzzy_match(name, license_number)
        
    #     report = MyReportGenerator(f'{proj_dir}/data/{name_}_anomaly.docx', 
    #                                data_1, data_2,
    #                                info_anomoly, info_mismatch,  
    #                                name)
    #     report.generate()
    print(batch_or_single_upload_anomaly(
        batch_generate_reports_anomaly(data_1, data_2, 
                                       'start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0', 
                                       use_mp = False), False))
        