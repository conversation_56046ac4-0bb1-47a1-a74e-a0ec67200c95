MODE: 'local'  # 'local' / 'test'/ 'formal'
POST: true

# =========== 本地环境 ===========
local:
  # 数据库配置
  ORI_DB:
    DB_HOST: '***********'
    DB_PORT: '5432'
    DB_NAME: 'pwxkz'
    DB_USER: 'pwxkz_ro'
    DB_PASSWD: 'ZfDpNgQXd0fn2'

  MAIN_DB:
    DB_HOST: '************'
    DB_PORT: '5432'
    DB_NAME: 'jiulongpo'
    DB_USER: 'jiulongpo'
    DB_PASSWD: '123456Ein!'

  # 大模型配置
  BASE_PATH : '/home/<USER>/project/large_model'
  API_KEY: "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
  EMB_MODEL: "gte-Qwen2-1.5B-instruct"
  EMB_API: "http://***************:8051/v1"
  LLM_NAME: "Qwen3-30B-A3B"
  LLM_API: "http://*************:8081/v1"
  RAG_API: "http://127.0.0.1:9380"
  DOCX_UPLOA_URL: 'https://stdn.cqjlst.cn:6010/law/system/oss/upload'
  CHANGE_TOKEN: false

## ========== 测试环境 ============
test:
  # 数据库配置
  ORI_DB:
    DB_HOST: '**************'
    DB_PORT: '6544'
    DB_NAME: 'pwxkz'
    DB_USER: 'pwxkz_ro'
    DB_PASSWD: 'ZfDpNgQXd0fn2'

  MAIN_DB:
    DB_HOST: '**************'
    DB_PORT: '55432'
    DB_NAME: 'jiulongpo'
    DB_USER: 'jiulongpo'
    DB_PASSWD: '123456Ein!'
  # 大模型配置
  BASE_PATH: '/workspace/data'
  API_KEY: "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
  EMB_MODEL: "gte-Qwen2-1.5B-instruct"
  EMB_API: "http://***************:8051/v1"
  LLM_NAME: "Qwen3-30B-A3B"
  LLM_API: "http://*************:8081/v1"
  RAG_API: "http://*************:9380"
  DOCX_UPLOA_URL: 'https://stdn.cqjlst.cn:6010/law/system/oss/upload'
  CHANGE_TOKEN: false

# ========== 政务云环境 ============
formal:
  # 正式环境
  ORI_DB:
    DB_HOST: '***********'
    DB_PORT: '15432'
    DB_NAME: 'dify_data'
    DB_USER: 'postgres'
    DB_PASSWD: 'difyai123456'

  MAIN_DB:
    DB_HOST: '***********'
    DB_PORT: '15432'
    DB_NAME: 'dify_data'
    DB_USER: 'postgres'
    DB_PASSWD: 'difyai123456'
  # 大模型配置
  BASE_PATH: '/workspace/data'
  API_KEY: "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
  EMB_MODEL: "rjmalagon/gte-qwen2-1.5b-instruct-embed-f16"
  EMB_API: "http://***********:8051"
  LLM_NAME: "/home/<USER>/AscendCloud/Qwen2.5-14B-Instruct-LoRA-AWQ-w4g128"
  LLM_API: "https://***********0:8001/v1/infers/0c7c827e-132d-49c9-809a-c817685500ef/v1"
  RAG_API: "http://**************:9380"
  DOCX_UPLOA_URL: 'http://*************:18097/prod-api/system/oss/upload'
  CHANGE_TOKEN: true


