FROM hub-dev.rockontrol.com/rk-ai-tools/env-ml-python3-pytorch:0.0.0-ce310b6
WORKDIR /workspace
ADD . /workspace

ENV LANG=zh_CN.UTF-8

RUN apt-get update && \
    apt-get install -y pandoc && \
    pip config --global set global.index-url http://pypi.douban.com/simple/ && \
    pip config --global set global.trusted-host pypi.douban.com && \
    pip3 install -r /workspace/requirements.txt -i  http://pypi.douban.com/simple/ && \
    rm -f requirements.txt && \
    pip cache purge

WORKDIR /workspace/bin

RUN rm -rf /workspace/bin/llm_key /workspace/bin/df_cache.pkl

# 不显示warning信息
ENTRYPOINT ["python3", "-W", "ignore", "web_server.py"]
CMD ["--mode", "formal", "--post", "true"]

