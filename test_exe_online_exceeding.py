#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_exe_online_exceeding.py
@Author: <PERSON>
@Date  : 2025/8/11
@Desc  : 测试执行报告与在线监测超标数据校验功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.check.check_exe_online_exceeding import check_exe_online_exceeding, clear_cache
from lib import logger
import json


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    city = '九龙坡区'
    start_date = '2024-01-01'
    end_date = '2024-01-31'
    
    try:
        # 清空缓存
        clear_cache()
        
        # 执行校验
        result = check_exe_online_exceeding(city, start_date, end_date)
        
        print(f"✅ 校验完成，发现问题记录: {len(result)} 条")
        
        # 显示前几条记录的详细信息
        if result:
            print("\n=== 问题记录示例 ===")
            for i, anomaly in enumerate(result[:3]):  # 只显示前3条
                print(f"\n问题记录 {i+1}:")
                print(f"  问题类型: {anomaly.get('issue_type', 'N/A')}")
                print(f"  描述: {anomaly.get('description', 'N/A')}")
                
                if 'exec_enterprise_info' in anomaly:
                    exec_info = anomaly['exec_enterprise_info']
                    print(f"  执行报告企业: {exec_info.get('f_enterprise_name', 'N/A')}")
                    print(f"  排口编号: {exec_info.get('outlet_id', 'N/A')}")
                
                if 'online_enterprise_info' in anomaly:
                    online_info = anomaly['online_enterprise_info']
                    print(f"  在线监测企业: {online_info.get('pollution_source_name', 'N/A')}")
                    print(f"  排放口ID: {online_info.get('emission_id', 'N/A')}")
                
                if 'pollutant_info' in anomaly:
                    pollutant_info = anomaly['pollutant_info']
                    print(f"  污染物: {pollutant_info.get('pollution_name', 'N/A')}")
                    print(f"  标准编码: {pollutant_info.get('std_code', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        logger.error(f"测试失败: {str(e)}")
        return False


def test_different_time_ranges():
    """测试不同时间范围"""
    print("\n=== 测试不同时间范围 ===")
    
    city = '九龙坡区'
    test_cases = [
        ('2024-01-01', '2024-01-07'),   # 一周
        ('2024-01-01', '2024-01-01'),   # 单天
        ('2023-12-01', '2023-12-31'),   # 一个月
    ]
    
    for start_date, end_date in test_cases:
        try:
            print(f"\n测试时间范围: {start_date} 至 {end_date}")
            result = check_exe_online_exceeding(city, start_date, end_date)
            print(f"✅ 发现问题记录: {len(result)} 条")
            
        except Exception as e:
            print(f"❌ 时间范围 {start_date} 至 {end_date} 测试失败: {str(e)}")


def test_invalid_inputs():
    """测试无效输入"""
    print("\n=== 测试无效输入 ===")
    
    invalid_cases = [
        ('', '2024-01-01', '2024-01-31'),           # 空城市名
        ('九龙坡区', '2024-13-01', '2024-01-31'),    # 无效日期
        ('九龙坡区', '2024-01-31', '2024-01-01'),    # 起始日期晚于结束日期
        ('不存在的城市', '2024-01-01', '2024-01-31'), # 不存在的城市
    ]
    
    for city, start_date, end_date in invalid_cases:
        try:
            result = check_exe_online_exceeding(city, start_date, end_date)
            print(f"❌ 应该失败但成功了: {city}, {start_date}, {end_date}")
        except Exception as e:
            print(f"✅ 正确捕获错误: {city}, {start_date}, {end_date} - {str(e)}")


def export_results_to_json(result, filename):
    """导出结果到JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 结果已导出到: {filename}")
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")


def main():
    """主测试函数"""
    print("开始测试执行报告与在线监测超标数据校验功能")
    print("=" * 60)
    
    # 测试基本功能
    success = test_basic_functionality()
    
    if success:
        # 测试不同时间范围
        test_different_time_ranges()
        
        # 测试无效输入
        test_invalid_inputs()
        
        # 执行一次完整测试并导出结果
        print("\n=== 执行完整测试并导出结果 ===")
        try:
            city = '九龙坡区'
            start_date = '2024-01-01'
            end_date = '2024-01-31'
            
            result = check_exe_online_exceeding(city, start_date, end_date)
            
            if result:
                export_results_to_json(result, 'exe_online_exceeding_results.json')
            else:
                print("无问题记录，跳过导出")
                
        except Exception as e:
            print(f"❌ 完整测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == '__main__':
    main()
